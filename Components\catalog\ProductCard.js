import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Package, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  AlertTriangle,
  BarChart3,
  Building2,
  Calendar
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";

export default function ProductCard({ product, onClick }) {
  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'growing':
        return <TrendingUp className="w-4 h-4 text-emerald-600" />;
      case 'declining':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <Minus className="w-4 h-4 text-slate-500" />;
    }
  };

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'growing':
        return 'bg-emerald-100 text-emerald-700 border-emerald-200';
      case 'declining':
        return 'bg-red-100 text-red-700 border-red-200';
      default:
        return 'bg-slate-100 text-slate-700 border-slate-200';
    }
  };

  const hasAlerts = () => {
    return product.frequency_trend === 'declining' || 
           product.total_variations > 5 ||
           (product.max_price - product.min_price) > (product.average_price * 0.3);
  };

  const getPriceVariation = () => {
    if (!product.min_price || !product.max_price) return 0;
    return ((product.max_price - product.min_price) / product.average_price * 100);
  };

  return (
    <Card 
      className="border-none shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 overflow-hidden"
      onClick={onClick}
    >
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className={`p-3 rounded-xl ${
                hasAlerts() ? 'bg-red-100 border border-red-200' : 'bg-purple-100 border border-purple-200'
              }`}>
                {hasAlerts() ? (
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                ) : (
                  <Package className="w-5 h-5 text-purple-600" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-bold text-slate-900 text-sm leading-tight line-clamp-2">
                  {product.main_name}
                </h3>
                <p className="text-xs text-slate-500 mt-1">
                  EAN: {product.ean}
                </p>
              </div>
            </div>
          </div>

          {/* Category and Trend */}
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs bg-slate-50">
              {product.category || 'Sem categoria'}
            </Badge>
            <Badge className={`${getTrendColor(product.frequency_trend)} border text-xs`}>
              {getTrendIcon(product.frequency_trend)}
              <span className="ml-1 capitalize">
                {product.frequency_trend === 'growing' ? 'Crescendo' :
                 product.frequency_trend === 'declining' ? 'Caindo' : 'Estável'}
              </span>
            </Badge>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="bg-slate-50 rounded-lg p-2">
              <p className="text-slate-500">Variações</p>
              <p className="font-bold text-slate-900">{product.total_variations}</p>
            </div>
            <div className="bg-slate-50 rounded-lg p-2">
              <p className="text-slate-500">Fornecedores</p>
              <p className="font-bold text-slate-900">{product.total_suppliers}</p>
            </div>
            <div className="bg-slate-50 rounded-lg p-2">
              <p className="text-slate-500">Entradas</p>
              <p className="font-bold text-slate-900">{product.total_entries}</p>
            </div>
            <div className="bg-slate-50 rounded-lg p-2">
              <p className="text-slate-500">Preço Médio</p>
              <p className="font-bold text-slate-900">
                {product.average_price ? 
                  new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(product.average_price) : 'N/A'
                }
              </p>
            </div>
          </div>

          {/* Price Range */}
          {product.min_price && product.max_price && (
            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span className="text-slate-500">Faixa de Preço</span>
                <span className={`font-medium ${
                  getPriceVariation() > 30 ? 'text-red-600' : 
                  getPriceVariation() > 15 ? 'text-amber-600' : 'text-emerald-600'
                }`}>
                  {getPriceVariation().toFixed(0)}% variação
                </span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-emerald-600 font-medium">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(product.min_price)}
                </span>
                <span className="text-red-600 font-medium">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(product.max_price)}
                </span>
              </div>
            </div>
          )}

          {/* Last Entry */}
          {product.last_entry && (
            <div className="flex items-center gap-2 text-xs text-slate-500 pt-2 border-t border-slate-100">
              <Calendar className="w-3 h-3" />
              <span>Última entrada: {formatDistanceToNow(new Date(product.last_entry), {
                addSuffix: true,
                locale: ptBR
              })}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}