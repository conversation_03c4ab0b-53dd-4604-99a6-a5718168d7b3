import React, { useState, useEffect } from "react";
import { Invoice, Driver, DeliveryRoute } from "@/entities/all";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Truck, 
  Plus, 
  Package,
  MapPin,
  Clock,
  CheckCircle,
  AlertTriangle
} from "lucide-react";

import DispatchSelector from "../components/dispatch/DispatchSelector";
import RouteManager from "../components/dispatch/RouteManager";
import DriverManager from "../components/dispatch/DriverManager";

export default function Dispatch() {
  const [verifiedInvoices, setVerifiedInvoices] = useState([]);
  const [drivers, setDrivers] = useState([]);
  const [activeRoutes, setActiveRoutes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("dispatch");

  useEffect(() => {
    loadDispatchData();
  }, []);

  const loadDispatchData = async () => {
    try {
      const [invoices, driversData, routes] = await Promise.all([
        Invoice.filter({ status: "verified" }, "-updated_date", 50),
        Driver.filter({ active: true }),
        DeliveryRoute.filter({ status: "on_route" }, "-departure_date")
      ]);
      
      setVerifiedInvoices(invoices);
      setDrivers(driversData);
      setActiveRoutes(routes);
    } catch (error) {
      console.error("Error loading dispatch data:", error);
    }
    setIsLoading(false);
  };

  const handleCreateRoute = async (selectedInvoices, driverId) => {
    try {
      const driver = drivers.find(d => d.id === driverId);
      const routeNumber = `R${Date.now().toString().slice(-6)}`;
      
      // Create route
      const route = await DeliveryRoute.create({
        driver_id: driverId,
        route_number: routeNumber,
        total_invoices: selectedInvoices.length,
        departure_date: new Date().toISOString()
      });

      // Update invoice statuses to dispatched
      for (const invoice of selectedInvoices) {
        await Invoice.update(invoice.id, { status: "dispatched" });
      }

      // Refresh data
      loadDispatchData();
      
      alert(`Rota ${routeNumber} criada com sucesso para ${driver.name}!`);
    } catch (error) {
      console.error("Error creating route:", error);
      alert("Erro ao criar rota. Tente novamente.");
    }
  };

  const getTabCounts = () => {
    return {
      dispatch: verifiedInvoices.length,
      routes: activeRoutes.length,
      drivers: drivers.length
    };
  };

  const tabCounts = getTabCounts();

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-slate-200 rounded w-1/3"></div>
          <div className="grid gap-4">
            {Array(3).fill(0).map((_, i) => (
              <div key={i} className="h-32 bg-slate-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="p-6 md:p-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <Truck className="w-8 h-8 text-indigo-600" />
              Sistema de Expedição
            </h1>
            <p className="text-slate-600 mt-1">
              Organize entregas, gerencie rotas e acompanhe motoristas
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Prontas p/ Expedição</p>
                  <p className="text-2xl font-bold text-slate-900">{verifiedInvoices.length}</p>
                </div>
                <div className="p-3 rounded-xl bg-emerald-50 border border-emerald-200">
                  <Package className="w-5 h-5 text-emerald-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Rotas Ativas</p>
                  <p className="text-2xl font-bold text-slate-900">{activeRoutes.length}</p>
                </div>
                <div className="p-3 rounded-xl bg-indigo-50 border border-indigo-200">
                  <MapPin className="w-5 h-5 text-indigo-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Motoristas Ativos</p>
                  <p className="text-2xl font-bold text-slate-900">{drivers.length}</p>
                </div>
                <div className="p-3 rounded-xl bg-purple-50 border border-purple-200">
                  <Truck className="w-5 h-5 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Entregas Hoje</p>
                  <p className="text-2xl font-bold text-slate-900">0</p>
                </div>
                <div className="p-3 rounded-xl bg-amber-50 border border-amber-200">
                  <CheckCircle className="w-5 h-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-2">
            <TabsList className="grid w-full grid-cols-3 bg-transparent gap-1">
              <TabsTrigger 
                value="dispatch"
                className="relative data-[state=active]:bg-emerald-50 data-[state=active]:text-emerald-700 data-[state=active]:border-emerald-200 border border-transparent rounded-lg px-4 py-2"
              >
                <Package className="w-4 h-4 mr-2" />
                Criar Expedição
                {tabCounts.dispatch > 0 && (
                  <Badge variant="secondary" className="ml-2 text-xs h-5 w-5 p-0 flex items-center justify-center">
                    {tabCounts.dispatch}
                  </Badge>
                )}
              </TabsTrigger>
              
              <TabsTrigger 
                value="routes"
                className="relative data-[state=active]:bg-indigo-50 data-[state=active]:text-indigo-700 data-[state=active]:border-indigo-200 border border-transparent rounded-lg px-4 py-2"
              >
                <MapPin className="w-4 h-4 mr-2" />
                Rotas Ativas
                {tabCounts.routes > 0 && (
                  <Badge variant="secondary" className="ml-2 text-xs h-5 w-5 p-0 flex items-center justify-center">
                    {tabCounts.routes}
                  </Badge>
                )}
              </TabsTrigger>
              
              <TabsTrigger 
                value="drivers"
                className="relative data-[state=active]:bg-purple-50 data-[state=active]:text-purple-700 data-[state=active]:border-purple-200 border border-transparent rounded-lg px-4 py-2"
              >
                <Truck className="w-4 h-4 mr-2" />
                Motoristas
                {tabCounts.drivers > 0 && (
                  <Badge variant="secondary" className="ml-2 text-xs h-5 w-5 p-0 flex items-center justify-center">
                    {tabCounts.drivers}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="dispatch">
            <DispatchSelector
              invoices={verifiedInvoices}
              drivers={drivers}
              onCreateRoute={handleCreateRoute}
            />
          </TabsContent>

          <TabsContent value="routes">
            <RouteManager
              routes={activeRoutes}
              drivers={drivers}
              onRouteUpdate={loadDispatchData}
            />
          </TabsContent>

          <TabsContent value="drivers">
            <DriverManager
              drivers={drivers}
              onDriverUpdate={loadDispatchData}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}