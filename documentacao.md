# Sistema de Conferência e Expedição de Pedidos - SaaS

## 🎯 Visão Geral

Sistema SaaS para automatizar e agilizar o processo de entrada, conferência e expedição de pedidos através da leitura de notas fiscais eletrônicas (NF-e), com foco em eficiência operacional, precisão na conferência e flexibilidade para diferentes tipos de clientes (supermercados e galpões de distribuição/redistribuição).

---

## 🏗️ Arquitetura Tecnológica

### Stack Principal

- **Frontend & Backend**: SvelteKit (SSR + API routes)
- **Banco de Dados**: Supabase (PostgreSQL)
- **ORM**: Drizzle ORM
- **Estilização**: TailwindCSS
- **Deploy**: Vercel
- **Arquitetura**: Modular com plugins

### Integrações Externas

- **API NF-e**: Terceirizada para download de XML via chave de acesso
- **Leitura de Código de Barras**: Scanner integrado no frontend

---

## 👥 Perfis de Usuário

| Perfil | Descrição | Permissões |
| --- | --- | --- |
| **Admin** | Administrador do sistema | Acesso total, gestão de organizações |
| **Gestor** | Gestor da operação | Todos os fluxos, dashboards, relatórios, resolução de ocorrências |
| **Operador** | Operador do sistema | Entrada de NF-e, expedição, geração de etiquetas, registros |
| **Conferente** | Responsável pela conferência | Conferência simples, por volume e detalhada |

---

## 📊 Modelo de Dados

### 1. Organizações (Multi-tenant)

```sql
CREATE TABLE organizacoes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nome VARCHAR(255) NOT NULL,
  cnpj VARCHAR(14) UNIQUE,
  plano VARCHAR(50) DEFAULT 'basic',
  status VARCHAR(20) DEFAULT 'ativo',
  created_at TIMESTAMP DEFAULT NOW()
);

```

### 2. Usuários

```sql
CREATE TABLE usuarios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organizacao_id UUID REFERENCES organizacoes(id) NOT NULL,
  nome VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  senha_hash VARCHAR(255) NOT NULL,
  perfil VARCHAR(20) NOT NULL, -- 'admin', 'gestor', 'operador', 'conferente'
  ativo BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

```

### 3. Nota Fiscal

```sql
CREATE TABLE nota_fiscal (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organizacao_id UUID REFERENCES organizacoes(id) NOT NULL,
  chave_acesso VARCHAR(44) NOT NULL,
  numero_nf VARCHAR(20) NOT NULL,
  emitente_nome VARCHAR(255) NOT NULL,
  emitente_cnpj VARCHAR(14) NOT NULL,
  destinatario_nome VARCHAR(255) NOT NULL,
  destinatario_cnpj VARCHAR(14),
  valor_total DECIMAL(10,2) NOT NULL,
  xml_content TEXT NOT NULL,
  status VARCHAR(20) DEFAULT 'disponivel',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),

  UNIQUE(organizacao_id, chave_acesso)
);

```

### 4. Itens da Nota Fiscal

```sql
CREATE TABLE item_nota_fiscal (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nota_fiscal_id UUID REFERENCES nota_fiscal(id) ON DELETE CASCADE,
  codigo_produto VARCHAR(100) NOT NULL,
  descricao VARCHAR(500) NOT NULL,
  unidade VARCHAR(10) NOT NULL,
  quantidade_nf DECIMAL(10,3) NOT NULL,
  valor_unitario DECIMAL(10,4) NOT NULL,
  valor_total DECIMAL(10,2) NOT NULL,
  ean VARCHAR(14), -- EAN específico para este item desta NF
  created_at TIMESTAMP DEFAULT NOW()
);

```

### 5. Sistema de Conferência

```sql
CREATE TABLE conferencia (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organizacao_id UUID REFERENCES organizacoes(id) NOT NULL,
  nota_fiscal_id UUID REFERENCES nota_fiscal(id) ON DELETE CASCADE,
  conferente_id UUID REFERENCES usuarios(id) NOT NULL,
  tipo_conferencia VARCHAR(20) NOT NULL, -- 'simples', 'volume', 'detalhada'
  codigo_volume VARCHAR(5), -- 'A', 'B', 'AA'... (atribuído no início)
  status VARCHAR(20) DEFAULT 'em_andamento',
  versao INTEGER DEFAULT 1,
  iniciada_em TIMESTAMP DEFAULT NOW(),
  finalizada_em TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE item_conferencia (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conferencia_id UUID REFERENCES conferencia(id) ON DELETE CASCADE,
  item_nota_fiscal_id UUID REFERENCES item_nota_fiscal(id) NOT NULL,
  quantidade_conferida DECIMAL(10,3) DEFAULT 0,
  caixa_numero INTEGER, -- 1, 2, 3... (B1, B2, B3 - só para detalhada)
  ean_utilizado VARCHAR(14),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE conferencia_historico (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conferencia_original_id UUID REFERENCES conferencia(id) NOT NULL,
  conferente_anterior_id UUID REFERENCES usuarios(id) NOT NULL,
  conferente_novo_id UUID REFERENCES usuarios(id) NOT NULL,
  versao_anterior INTEGER NOT NULL,
  motivo VARCHAR(500),
  reiniciada_em TIMESTAMP DEFAULT NOW()
);

```

### 6. Controle de Códigos de Volume

```sql
CREATE TABLE codigo_volume_controle (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organizacao_id UUID REFERENCES organizacoes(id) NOT NULL,
  codigo VARCHAR(5) NOT NULL, -- 'A', 'B', 'AA', 'AB'...
  nota_fiscal_id UUID REFERENCES nota_fiscal(id),
  usado_em TIMESTAMP DEFAULT NOW(),
  liberado_em TIMESTAMP, -- Quando NF foi finalizada
  status VARCHAR(20) DEFAULT 'em_uso',

  UNIQUE(organizacao_id, codigo, status)
);

```

### 7. Sistema de Catálogo de Produtos

```sql
-- Tabela principal de produtos (por EAN único)
CREATE TABLE produtos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organizacao_id UUID REFERENCES organizacoes(id) NOT NULL,
  ean VARCHAR(14) NOT NULL,
  nome_principal VARCHAR(500), -- Nome "oficial" escolhido pelo gestor
  categoria VARCHAR(100),
  unidade_padrao VARCHAR(10),
  ativo BOOLEAN DEFAULT true,
  criado_em TIMESTAMP DEFAULT NOW(),
  atualizado_em TIMESTAMP DEFAULT NOW(),

  UNIQUE(organizacao_id, ean)
);

-- Tabela de variações de nomes para cada produto
CREATE TABLE produto_variacoes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  produto_id UUID REFERENCES produtos(id) ON DELETE CASCADE,
  nome_variacao VARCHAR(500) NOT NULL,
  fornecedor_cnpj VARCHAR(14),
  fornecedor_nome VARCHAR(255),
  primeira_ocorrencia TIMESTAMP DEFAULT NOW(),
  ultima_ocorrencia TIMESTAMP DEFAULT NOW(),
  total_ocorrencias INTEGER DEFAULT 1,

  UNIQUE(produto_id, nome_variacao, fornecedor_cnpj)
);

-- Tabela de histórico de entradas do produto
CREATE TABLE produto_historico_entradas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  produto_id UUID REFERENCES produtos(id) ON DELETE CASCADE,
  item_nota_fiscal_id UUID REFERENCES item_nota_fiscal(id) NOT NULL,
  nome_usado VARCHAR(500) NOT NULL,
  fornecedor_cnpj VARCHAR(14),
  fornecedor_nome VARCHAR(255),
  quantidade DECIMAL(10,3) NOT NULL,
  valor_unitario DECIMAL(10,4) NOT NULL,
  valor_total DECIMAL(10,2) NOT NULL,
  conferente_id UUID REFERENCES usuarios(id),
  data_entrada TIMESTAMP DEFAULT NOW(),

  UNIQUE(item_nota_fiscal_id) -- Cada item de NF só pode ter uma entrada no histórico
);

-- Tabela de estatísticas calculadas (cache para performance)
CREATE TABLE produto_estatisticas (
  produto_id UUID PRIMARY KEY REFERENCES produtos(id) ON DELETE CASCADE,
  total_variacoes_nome INTEGER DEFAULT 0,
  total_fornecedores INTEGER DEFAULT 0,
  total_entradas INTEGER DEFAULT 0,
  preco_medio DECIMAL(10,4),
  preco_minimo DECIMAL(10,4),
  preco_maximo DECIMAL(10,4),
  primeira_entrada TIMESTAMP,
  ultima_entrada TIMESTAMP,
  frequencia_mensal DECIMAL(10,2), -- Média de entradas por mês
  dias_sem_entrada INTEGER, -- Dias desde última entrada
  tendencia VARCHAR(20), -- 'crescente', 'estavel', 'decrescente'
  atualizado_em TIMESTAMP DEFAULT NOW()
);

```

### 8. Sistema de Expedição

```sql
-- Tabela de motoristas/entregadores
CREATE TABLE entregadores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organizacao_id UUID REFERENCES organizacoes(id) NOT NULL,
  nome VARCHAR(255) NOT NULL,
  telefone VARCHAR(20),
  documento VARCHAR(20),
  modelo_pagamento VARCHAR(20) NOT NULL, -- 'fixo_diario', 'fixo_mais_rota', 'por_entrega'
  valor_fixo DECIMAL(10,2), -- Valor fixo (se aplicável)
  valor_por_rota DECIMAL(10,2), -- Valor adicional por rota (se aplicável)
  valor_por_entrega DECIMAL(10,2), -- Valor por NF entregue (se aplicável)
  forma_pagamento VARCHAR(20) DEFAULT 'mensal', -- 'diaria', 'quinzenal', 'mensal'
  ativo BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Tabela de romaneios (lotes de entrega)
CREATE TABLE romaneios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organizacao_id UUID REFERENCES organizacoes(id) NOT NULL,
  entregador_id UUID REFERENCES entregadores(id) NOT NULL,
  numero_romaneio VARCHAR(20) NOT NULL,
  status VARCHAR(20) DEFAULT 'em_rota', -- 'em_rota', 'finalizado'
  data_saida TIMESTAMP DEFAULT NOW(),
  data_retorno TIMESTAMP,
  observacoes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),

  UNIQUE(organizacao_id, numero_romaneio)
);

-- Relacionamento NF-e com romaneios
CREATE TABLE romaneio_notas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  romaneio_id UUID REFERENCES romaneios(id) ON DELETE CASCADE,
  nota_fiscal_id UUID REFERENCES nota_fiscal(id) NOT NULL,
  status_entrega VARCHAR(20) DEFAULT 'em_rota', -- 'em_rota', 'entregue', 'ocorrencia'
  data_entrega TIMESTAMP,
  observacoes_entrega TEXT,

  UNIQUE(romaneio_id, nota_fiscal_id)
);

```

### 9. Sistema de Ocorrências

```sql
CREATE TABLE ocorrencias (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organizacao_id UUID REFERENCES organizacoes(id) NOT NULL,
  nota_fiscal_id UUID REFERENCES nota_fiscal(id) NOT NULL,
  tipo_origem VARCHAR(20) NOT NULL, -- 'conferencia', 'expedicao', 'manual'
  tipo_problema VARCHAR(50) NOT NULL, -- 'produto_danificado', 'endereco_incorreto', etc.
  descricao TEXT NOT NULL,
  valor_impacto DECIMAL(10,2),
  responsavel_id UUID REFERENCES usuarios(id) NOT NULL,
  status VARCHAR(20) DEFAULT 'aberta', -- 'aberta', 'investigacao', 'resolvida', 'cancelada'
  data_ocorrencia TIMESTAMP DEFAULT NOW(),
  data_resolucao TIMESTAMP,
  resolucao TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

```

### 10. Módulo Financeiro (Opcional)

```sql
-- Controle de boletos extraídos das NF-es
CREATE TABLE boletos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organizacao_id UUID REFERENCES organizacoes(id) NOT NULL,
  nota_fiscal_id UUID REFERENCES nota_fiscal(id) NOT NULL,
  numero_boleto VARCHAR(100) NOT NULL,
  valor DECIMAL(10,2) NOT NULL,
  data_vencimento DATE NOT NULL,
  status VARCHAR(20) DEFAULT 'pendente', -- 'pendente', 'pago', 'vencido', 'cancelado'
  data_pagamento DATE,
  observacoes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),

  UNIQUE(organizacao_id, numero_boleto)
);

-- Controle de repasses para entregadores
CREATE TABLE entregador_repasses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  entregador_id UUID REFERENCES entregadores(id) NOT NULL,
  periodo_inicio DATE NOT NULL,
  periodo_fim DATE NOT NULL,
  total_entregas INTEGER DEFAULT 0,
  valor_fixo DECIMAL(10,2) DEFAULT 0,
  valor_rotas DECIMAL(10,2) DEFAULT 0,
  valor_entregas DECIMAL(10,2) DEFAULT 0,
  valor_total DECIMAL(10,2) NOT NULL,
  valor_antecipacoes DECIMAL(10,2) DEFAULT 0,
  valor_liquido DECIMAL(10,2) NOT NULL,
  status VARCHAR(20) DEFAULT 'pendente', -- 'pendente', 'pago'
  data_pagamento TIMESTAMP,
  observacoes TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

```

---

## 📋 Estados da NF-e

| Estado | Descrição | Próximas Ações Possíveis |
| --- | --- | --- |
| `disponivel` | XML baixado e pronto para conferência | Iniciar Conferência |
| `em_conferencia` | Conferência em andamento | Pausar, Finalizar, Reiniciar |
| `pausada` | Conferência pausada | Retomar Conferência |
| `conferida` | Conferência finalizada | Selecionar para Expedição, Revisar |
| `despachada` | Enviada com motorista | Finalizar ou Registrar Ocorrência |
| `finalizada` | Entregue com sucesso | Apenas consulta |
| `ocorrencia` | Problema identificado | Resolver Ocorrência |

---

## 🔄 Fluxos Principais

### 1. Entrada da Nota Fiscal

**Objetivo**: Inicializar o processo via escaneamento da chave de acesso da NF-e.

### Fluxo:

1. **Entrada Individual**: Escanear código de barras (chave de acesso)
2. **Entrada em Lote**: Escanear múltiplas chaves sequencialmente
3. **Upload Manual**: Upload de arquivos XML direto
4. **Download Automático**: Sistema baixa XML via API terceirizada
5. **Processamento do Catálogo**: Atualização automática do catálogo de produtos
6. **Armazenamento**: XML salvo no banco, status = `disponivel`

### Processamento Automático do Catálogo:

- Para cada item da NF-e, sistema verifica se o EAN já existe no catálogo
- **EAN novo**: Cria produto no catálogo com nome da NF-e como nome principal
- **EAN existente**: Verifica se o nome é uma nova variação e registra histórico
- Atualiza estatísticas automaticamente (frequência, preços, fornecedores)

### Validações:

- Chave de acesso única por organização
- XML válido e bem formado
- Sucesso no download via API terceirizada

---

### 2. Conferência de Produtos

**Objetivo**: Validar produtos e organizá-los conforme necessidade operacional.

### Tipos de Conferência:

| Tipo | Código | Características | Etiquetas |
| --- | --- | --- | --- |
| **Simples** | — | Apenas conferência de quantidades | Não gera |
| **Por Volume** | A, B, C... | Um código único para toda a NF-e | Etiqueta: "A - Cliente - NF 12345 - 5 volumes" |
| **Detalhada** | A1, A2, A3... | Produtos distribuídos por caixas numeradas | Etiqueta: "A1 - Cliente - NF 12345 - 5 volumes" |

### Sistema de Códigos:

- **Sequência**: A-Z (26) → AA-ZZ (676 possibilidades)
- **Reutilização**: Apenas quando NF-e estiver `finalizada`
- **Atribuição**: No momento de iniciar a conferência

### Fluxo de Conferência:

1. **Seleção**: Escolher NF-e(s) com status `disponivel` + Conferente
2. **Tipo**: Definir tipo de conferência (simples/volume/detalhada)
3. **Código**: Sistema atribui próximo código disponível (se aplicável)
4. **Execução**:
    - **Manual**: Botões +/- para ajustar quantidades
    - **Scanner**: Leitura de EAN e associação automática ao produto do catálogo
5. **Acompanhamento**: Progresso em tempo real (% por NF e por item)
6. **Alimentação do Catálogo**: Dados de conferência atualizam histórico do produto

### Integração com Catálogo na Conferência:

- Scanner lê EAN → Sistema busca no catálogo → Exibe informações do produto
- Mostra nome principal, variações conhecidas e estatísticas rápidas
- Alerta se é produto novo ou se tem comportamento anormal (preço, fornecedor)
- Registra dados da conferência no histórico de entradas do produto

---

## 📦 Sistema de Catálogo Inteligente

### Visão Geral

O catálogo é o coração inteligente do sistema, sendo alimentado automaticamente a cada NF-e processada e fornecendo insights valiosos sobre o comportamento dos produtos.

### Funcionamento Automático

### Entrada Automática

- **Toda NF-e processada** alimenta automaticamente o catálogo
- **Primeiro encontro**: Produto é criado com o nome que veio na NF-e
- **Próximos encontros**: Sistema verifica se já conhece aquele nome para aquele EAN
- **Nome novo**: Adiciona como nova variação, mantém histórico de quem trouxe e quando

### Sistema de Variações

Um único EAN pode ter **múltiplas variações de nome**:

**Exemplo:**

- **EAN**: 7894900011517
- **Variação 1**: "COCA-COLA 2L PET" (Fornecedor ABC - 8 ocorrências)
- **Variação 2**: "REFRIGERANTE COCA COLA 2000ML" (Fornecedor XYZ - 3 ocorrências)
- **Variação 3**: "COCA COLA 2 LITROS" (Fornecedor ABC - 2 ocorrências)
- **Variação 4**: "REF.COCA-COLA PET 2L" (Fornecedor 123 - 1 ocorrência)

### Inteligência e Estatísticas

### Cálculos Automáticos

O sistema calcula automaticamente:

- **Total de variações** de nome já registradas
- **Quantos fornecedores** diferentes já trouxeram aquele produto
- **Frequência de entrada**: quantas vezes por mês em média
- **Preços**: médio, mínimo e máximo já registrados
- **Intervalos**: maior tempo sem entrada do produto
- **Tendências**: se está entrando mais ou menos ultimamente

### Gestão de Nome Principal

- **Inicial**: Primeiro nome que apareceu
- **Editável**: Gestor pode escolher qual nome usar como "oficial"
- **Sugestão automática**: Sistema pode sugerir o nome mais comum como principal

### Alertas Inteligentes

- **Produto novo**: "Primeira vez que vemos este EAN"
- **Nome novo**: "Este produto apareceu com um nome diferente"
- **Preço anormal**: "Preço 30% acima da média histórica"
- **Ausência prolongada**: "Este produto não entra há 45 dias"
- **Fornecedor novo**: "Primeiro produto deste fornecedor"

### Interface do Catálogo

### Página Principal

- Lista todos os produtos com filtros por categoria, fornecedor, atividade
- Busca por EAN, nome ou variação
- Indicadores visuais de atividade (cores por frequência)
- Alertas destacados para produtos com comportamento anormal

### Página Individual do Produto

- **Cabeçalho**: EAN, nome principal, categoria
- **Variações**: Lista todas as variações com estatísticas
- **Estatísticas**: Gráficos de entrada, preços, fornecedores
- **Histórico Completo**: Timeline de todas as entradas
- **Alertas**: Lista de alertas específicos deste produto

---

### 3. Gestão de NF-e

**Objetivo**: Centralizar controle e acompanhamento de todas as NF-es.

### Interface:

- **Abas por Status**: Disponíveis, Em Conferência, Pausadas, Conferidas, Despachadas, Finalizadas, Ocorrências
- **Ações Rápidas**:
    - `Disponível` → Iniciar Conferência
    - `Pausada` → Retomar Conferência
    - `Conferida` → Selecionar para Expedição ou Revisar
    - `Despachada` → Finalizar ou Registrar Ocorrência

### Funcionalidades:

- **Paginação**: Performance otimizada para grandes volumes
- **Filtros**: Data, emitente, valor, status
- **Busca**: Por número NF, CNPJ, chave de acesso
- **Exportação**: Relatórios em PDF/Excel
- **Insights do Catálogo**: Alertas sobre produtos das NF-es selecionadas

---

### 4. Expedição (Dispatch)

**Objetivo**: Organizar saída das mercadorias e controlar entregas.

### Fluxo:

1. **Seleção do Motorista**: Escolher entre motoristas cadastrados
2. **Seleção de NF-es**: Apenas NF-es com status `conferida`
3. **Geração do Romaneio**: Documento com:
    - Nome do motorista e data
    - Lista de NF-es com destinatários
    - Códigos de volume (se aplicável)
    - Campos para assinatura dos clientes
    - Observações manuais
4. **Status**: NF-es vão para `despachada`, Romaneio fica `em_rota`

### Retorno do Motorista:

1. **Finalização da Rota**: Revisar cada NF-e do romaneio
2. **Para cada NF-e**:
    - ✅ **Finalizar**: Entregue com sucesso → status `finalizada`
    - ⚠️ **Ocorrência**: Problema na entrega → status `ocorrencia`

---

### 5. Gestão de Ocorrências

**Objetivo**: Registrar e resolver problemas operacionais.

### Origens de Ocorrências:

- **Conferência**: Divergências automáticas
- **Expedição**: Problemas na entrega
- **Manual**: Criação direta pelo operador
- **Catálogo**: Alertas automáticos de produtos

### Tipos de Problemas:

- **Produtos**: Danificados, faltando, avariados, preço anormal
- **Logística**: Endereço incorreto, cliente ausente, recusa
- **Transporte**: Roubo, acidente, veículo com defeito
- **Catálogo**: Produto novo suspeito, variação inconsistente
- **Outros**: Campo livre para descrição

---

### 6. Gestão de Entregadores

### Cadastro:

- **Dados**: Nome, telefone, documento
- **Modelo de Pagamento**:
    - Fixo por dia (R$ 300/dia)
    - Fixo + taxa por rota (R$ 200 + R$ 100/rota)
    - Por entrega (valor por NF-e entregue)
- **Forma de Pagamento**: Diária, quinzenal, mensal

### Controle Financeiro:

- **Histórico de Entregas**: Todas NF-es entregues por período
- **Cálculo de Repasse**: Automático conforme modelo
- **Antecipações**: Registro de adiantamentos
- **Relatórios**: Ganhos por período, pendências

---

### 7. Módulo Financeiro (Opcional)

**Ativação**: Apenas para galpões que fazem pagamento de boletos.

### Boletos:

- **Extração**: Automática do XML da NF-e
- **Controle**: Número, valor, vencimento, status
- **Relatórios**: Por período, situação, valor

### Repasse de Motoristas:

- **Valores Pendentes**: Cálculo automático
- **Histórico de Pagamentos**: Data, valor, observações
- **Antecipações**: Controle de adiantamentos
- **Dashboard**: Visão geral financeira

---

## 🔍 Rastreabilidade Completa

### Timeline Automática por NF-e:

```
📥 Entrada: 18/07/2025 - 08:34 (3 produtos novos detectados)
📦 Conferência: Volume Detalhado (Conferente: João) - 18/07/2025 - 09:10
🔍 Catálogo: 2 alertas de preço, 1 fornecedor novo
🚚 Expedição: Motorista Carlos - 18/07/2025 - 11:05
✅ Finalizada: 18/07/2025 - 17:50

```

### Registros Automáticos:

- **Entrada**: Data/hora do XML + análise do catálogo
- **Conferência**: Tipo, conferente, início/fim + dados dos produtos
- **Expedição**: Motorista, romaneio, saída
- **Finalização**: Status final, observações
- **Ocorrências**: Tipo, descrição, resolução
- **Catálogo**: Atualizações automáticas de estatísticas

---

## 🚀 Otimizações para SaaS

### Performance:

```sql
-- Índices principais
CREATE INDEX idx_nf_org_status_created ON nota_fiscal(organizacao_id, status, created_at DESC);
CREATE INDEX idx_conferencia_org_status ON conferencia(organizacao_id, status);
CREATE INDEX idx_usuarios_org_perfil ON usuarios(organizacao_id, perfil) WHERE ativo = true;
CREATE INDEX idx_produtos_org_ean ON produtos(organizacao_id, ean);
CREATE INDEX idx_produto_historico_data ON produto_historico_entradas(produto_id, data_entrada DESC);
CREATE INDEX idx_produto_variacoes_produto ON produto_variacoes(produto_id, total_ocorrencias DESC);

```

### Segurança Multi-tenant:

- Row Level Security (RLS) no Supabase
- Isolamento completo entre organizações
- Autenticação JWT com claims de organização

### Escalabilidade:

- Paginação em todas as listagens
- Lazy loading de dados pesados (XML)
- Cache de consultas frequentes e estatísticas do catálogo
- Otimização de queries N+1
- Jobs em background para cálculo de estatísticas do catálogo

---

## 📱 Interface e UX

### Princípios de Design:

- **Mobile First**: Otimizado para tablets e celulares
- **Scanner Integration**: Leitura de códigos de barras nativa
- **Real-time Updates**: Progresso em tempo real
- **Offline Capability**: Funcionalidades básicas offline
- **Smart Insights**: Alertas e sugestões inteligentes do catálogo

### Componentes Principais:

- **Dashboard**: Visão geral operacional + insights do catálogo
- **Scanner**: Leitor de códigos integrado com busca no catálogo
- **Cards de NF-e**: Informações condensadas e alertas do catálogo
- **Catálogo Visual**: Interface rica para exploração de produtos
- **Formulários Inteligentes**: Validação e preenchimento automático
- **Relatórios Visuais**: Gráficos e métricas operacionais e do catálogo

---

## 🔧 Roadmap de Desenvolvimento

### MVP (Versão 1.0):

- [ ]  Entrada de NF-e (scanner + upload)
- [ ]  Conferência simples
- [ ]  Catálogo básico (criação automática)
- [ ]  Gestão básica de NF-e
- [ ]  Usuários e permissões

### Versão 2.0:

- [ ]  Conferência por volume e detalhada
- [ ]  Sistema de expedição completo
- [ ]  Catálogo avançado (variações e estatísticas)
- [ ]  Alertas inteligentes
- [ ]  Gestão de ocorrências
- [ ]  Relatórios básicos

### Versão 3.0:

- [ ]  Módulo financeiro
- [ ]  Gestão de entregadores
- [ ]  Dashboard avançado com IA
- [ ]  Análises preditivas do catálogo
- [ ]  Integrações externas (ERPs, marketplaces)

### Versão 4.0:

- [ ]  Machine Learning para otimização de estoque
- [ ]  Previsão de demanda baseada no catálogo
- [ ]  Integração com fornecedores
- [ ]  API pública para integrações

---

*Documentação reorganizada e atualizada em: 20/07/2025*

## Painel para Admin

Você pode ter um painel administrativo **global (superadmin)** para:

- Visualizar todas as organizações
- Ver uso por organização (quantidade de NF, usuários ativos etc.)
- Ativar/desativar plugins por cliente
- Resetar senhas
- Ver logs globais