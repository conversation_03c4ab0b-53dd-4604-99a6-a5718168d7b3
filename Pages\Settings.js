import React, { useState, useEffect } from "react";
import { User, Organization } from "@/entities/all";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Settings as SettingsIcon, 
  Users, 
  Shield, 
  Building2, 
  Plug, 
  Bell,
  Plus,
  Edit2,
  Trash2,
  Save,
  Key
} from "lucide-react";

import UserManagement from "../components/settings/UserManagement";
import OrganizationSettings from "../components/settings/OrganizationSettings";
import IntegrationSettings from "../components/settings/IntegrationSettings";
import NotificationSettings from "../components/settings/NotificationSettings";

export default function Settings() {
  const [currentUser, setCurrentUser] = useState(null);
  const [organization, setOrganization] = useState(null);
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("organization");

  useEffect(() => {
    loadSettingsData();
  }, []);

  const loadSettingsData = async () => {
    try {
      const user = await User.me();
      setCurrentUser(user);
      
      // Load organization data
      if (user.organization_id) {
        const org = await Organization.get(user.organization_id);
        setOrganization(org);
      }
      
      // Load users if admin/manager
      if (user.role === 'admin' || user.role === 'manager') {
        const userData = await User.list();
        setUsers(userData);
      }
    } catch (error) {
      console.error("Error loading settings data:", error);
    }
    setIsLoading(false);
  };

  const tabs = [
    { value: "organization", label: "Organização", icon: Building2, roles: ["admin", "manager"] },
    { value: "users", label: "Usuários", icon: Users, roles: ["admin", "manager"] },
    { value: "permissions", label: "Permissões", icon: Shield, roles: ["admin"] },
    { value: "integrations", label: "Integrações", icon: Plug, roles: ["admin", "manager"] },
    { value: "notifications", label: "Notificações", icon: Bell, roles: ["admin", "manager", "operator", "verifier"] }
  ];

  const filteredTabs = tabs.filter(tab => 
    !currentUser || tab.roles.includes(currentUser.role)
  );

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-slate-200 rounded w-1/3"></div>
          <div className="grid gap-4">
            {Array(3).fill(0).map((_, i) => (
              <div key={i} className="h-32 bg-slate-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="p-6 md:p-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <SettingsIcon className="w-8 h-8 text-slate-600" />
              Configurações do Sistema
            </h1>
            <p className="text-slate-600 mt-1">
              Gerencie configurações da organização, usuários e integrações
            </p>
          </div>
          {currentUser?.role === 'admin' && (
            <Badge className="bg-amber-100 text-amber-700 border-amber-200">
              Administrador
            </Badge>
          )}
        </div>

        {/* Settings Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-2">
            <TabsList className="grid w-full bg-transparent gap-1" style={{gridTemplateColumns: `repeat(${filteredTabs.length}, 1fr)`}}>
              {filteredTabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger 
                    key={tab.value} 
                    value={tab.value}
                    className="relative data-[state=active]:bg-slate-50 data-[state=active]:text-slate-700 data-[state=active]:border-slate-200 border border-transparent rounded-lg px-3 py-2"
                  >
                    <Icon className="w-4 h-4 mr-2" />
                    <span className="text-sm font-medium">{tab.label}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </div>

          <TabsContent value="organization">
            <OrganizationSettings 
              organization={organization}
              currentUser={currentUser}
              onUpdate={loadSettingsData}
            />
          </TabsContent>

          <TabsContent value="users">
            <UserManagement 
              users={users}
              currentUser={currentUser}
              onUpdate={loadSettingsData}
            />
          </TabsContent>

          <TabsContent value="permissions">
            <Card className="border-none shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5 text-emerald-600" />
                  Gestão de Permissões
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Role Permissions Matrix */}
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3 font-medium text-slate-900">Funcionalidade</th>
                          <th className="text-center p-3 font-medium text-slate-900">Admin</th>
                          <th className="text-center p-3 font-medium text-slate-900">Gestor</th>
                          <th className="text-center p-3 font-medium text-slate-900">Operador</th>
                          <th className="text-center p-3 font-medium text-slate-900">Conferente</th>
                        </tr>
                      </thead>
                      <tbody>
                        {[
                          { name: "Dashboard", admin: true, manager: true, operator: true, verifier: true },
                          { name: "Notas Fiscais", admin: true, manager: true, operator: true, verifier: true },
                          { name: "Catálogo", admin: true, manager: true, operator: true, verifier: false },
                          { name: "Conferência", admin: true, manager: true, operator: false, verifier: true },
                          { name: "Expedição", admin: true, manager: true, operator: true, verifier: false },
                          { name: "Ocorrências", admin: true, manager: true, operator: true, verifier: true },
                          { name: "Motoristas", admin: true, manager: true, operator: false, verifier: false },
                          { name: "Financeiro", admin: true, manager: true, operator: false, verifier: false },
                          { name: "Configurações", admin: true, manager: true, operator: false, verifier: false }
                        ].map((perm, index) => (
                          <tr key={index} className="border-b hover:bg-slate-50">
                            <td className="p-3 font-medium">{perm.name}</td>
                            <td className="text-center p-3">
                              {perm.admin ? (
                                <Badge className="bg-emerald-100 text-emerald-700">✓</Badge>
                              ) : (
                                <Badge variant="outline">✗</Badge>
                              )}
                            </td>
                            <td className="text-center p-3">
                              {perm.manager ? (
                                <Badge className="bg-emerald-100 text-emerald-700">✓</Badge>
                              ) : (
                                <Badge variant="outline">✗</Badge>
                              )}
                            </td>
                            <td className="text-center p-3">
                              {perm.operator ? (
                                <Badge className="bg-emerald-100 text-emerald-700">✓</Badge>
                              ) : (
                                <Badge variant="outline">✗</Badge>
                              )}
                            </td>
                            <td className="text-center p-3">
                              {perm.verifier ? (
                                <Badge className="bg-emerald-100 text-emerald-700">✓</Badge>
                              ) : (
                                <Badge variant="outline">✗</Badge>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="integrations">
            <IntegrationSettings 
              currentUser={currentUser}
              onUpdate={loadSettingsData}
            />
          </TabsContent>

          <TabsContent value="notifications">
            <NotificationSettings 
              currentUser={currentUser}
              onUpdate={loadSettingsData}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}