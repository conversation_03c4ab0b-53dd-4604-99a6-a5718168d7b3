import React, { useState, useEffect } from "react";
import { Invoice, InvoiceItem } from "@/entities/all";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  FileText, 
  Plus, 
  Search, 
  Upload, 
  Filter,
  Building2,
  DollarSign,
  Calendar,
  MoreVertical
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

import InvoiceUpload from "../components/invoices/InvoiceUpload";
import InvoiceCard from "../components/invoices/InvoiceCard";
import InvoiceFilters from "../components/invoices/InvoiceFilters";

export default function Invoices() {
  const [invoices, setInvoices] = useState([]);
  const [filteredInvoices, setFilteredInvoices] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showUpload, setShowUpload] = useState(false);
  const [activeTab, setActiveTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({
    status: "all",
    dateRange: "all",
    minValue: "",
    maxValue: ""
  });

  useEffect(() => {
    loadInvoices();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [invoices, activeTab, searchTerm, filters]);

  const loadInvoices = async () => {
    try {
      const data = await Invoice.list("-created_date", 100);
      setInvoices(data);
    } catch (error) {
      console.error("Error loading invoices:", error);
    }
    setIsLoading(false);
  };

  const applyFilters = () => {
    let filtered = invoices;

    // Filter by tab/status
    if (activeTab !== "all") {
      filtered = filtered.filter(invoice => invoice.status === activeTab);
    }

    // Apply search
    if (searchTerm) {
      filtered = filtered.filter(invoice => 
        invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.issuer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.access_key.includes(searchTerm)
      );
    }

    // Apply additional filters
    if (filters.status !== "all") {
      filtered = filtered.filter(invoice => invoice.status === filters.status);
    }

    if (filters.minValue) {
      filtered = filtered.filter(invoice => invoice.total_value >= parseFloat(filters.minValue));
    }

    if (filters.maxValue) {
      filtered = filtered.filter(invoice => invoice.total_value <= parseFloat(filters.maxValue));
    }

    setFilteredInvoices(filtered);
  };

  const getStatusCounts = () => {
    return invoices.reduce((acc, invoice) => {
      acc[invoice.status] = (acc[invoice.status] || 0) + 1;
      acc.all = (acc.all || 0) + 1;
      return acc;
    }, {});
  };

  const statusCounts = getStatusCounts();

  const statusTabs = [
    { value: "all", label: "Todas", count: statusCounts.all || 0 },
    { value: "available", label: "Disponível", count: statusCounts.available || 0 },
    { value: "in_verification", label: "Em Conferência", count: statusCounts.in_verification || 0 },
    { value: "verified", label: "Conferidas", count: statusCounts.verified || 0 },
    { value: "dispatched", label: "Despachadas", count: statusCounts.dispatched || 0 },
    { value: "completed", label: "Finalizadas", count: statusCounts.completed || 0 },
    { value: "issue", label: "Ocorrências", count: statusCounts.issue || 0 },
  ];

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-slate-200 rounded w-1/4"></div>
          <div className="grid gap-4">
            {Array(5).fill(0).map((_, i) => (
              <div key={i} className="h-24 bg-slate-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="p-6 md:p-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <FileText className="w-8 h-8 text-blue-600" />
              Notas Fiscais Eletrônicas
            </h1>
            <p className="text-slate-600 mt-1">
              Gerencie e acompanhe todas as NF-e do sistema
            </p>
          </div>
          <Button 
            onClick={() => setShowUpload(true)}
            className="bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700 text-white shadow-lg"
          >
            <Plus className="w-4 h-4 mr-2" />
            Nova NF-e
          </Button>
        </div>

        {/* Upload Modal */}
        {showUpload && (
          <InvoiceUpload 
            onClose={() => setShowUpload(false)}
            onSuccess={loadInvoices}
          />
        )}

        {/* Search and Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <Input
                placeholder="Buscar por número, emitente ou chave de acesso..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <InvoiceFilters 
              filters={filters}
              onFiltersChange={setFilters}
            />
          </div>
        </div>

        {/* Status Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-2">
            <TabsList className="grid w-full grid-cols-7 bg-transparent gap-1">
              {statusTabs.map((tab) => (
                <TabsTrigger 
                  key={tab.value} 
                  value={tab.value}
                  className="relative data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 border border-transparent rounded-lg px-3 py-2"
                >
                  <span className="text-sm font-medium">{tab.label}</span>
                  {tab.count > 0 && (
                    <Badge 
                      variant="secondary" 
                      className="ml-2 text-xs h-5 w-5 p-0 flex items-center justify-center bg-slate-100 text-slate-700"
                    >
                      {tab.count}
                    </Badge>
                  )}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          <TabsContent value={activeTab} className="space-y-4">
            {filteredInvoices.length === 0 ? (
              <Card className="border-none shadow-lg">
                <CardContent className="text-center py-12">
                  <FileText className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-slate-900 mb-2">
                    Nenhuma NF-e encontrada
                  </h3>
                  <p className="text-slate-500 mb-6">
                    {activeTab === "all" 
                      ? "Comece fazendo upload da primeira nota fiscal"
                      : `Não há notas fiscais com status "${statusTabs.find(t => t.value === activeTab)?.label}"`
                    }
                  </p>
                  {activeTab === "all" && (
                    <Button 
                      onClick={() => setShowUpload(true)}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Fazer Upload
                    </Button>
                  )}
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-4">
                {filteredInvoices.map((invoice) => (
                  <InvoiceCard 
                    key={invoice.id} 
                    invoice={invoice}
                    onUpdate={loadInvoices}
                  />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}