import React, { useState, useEffect } from "react";
import { Issue, Invoice, User } from "@/entities/all";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  AlertTriangle, 
  Plus, 
  Eye,
  CheckCircle,
  Clock,
  XCircle
} from "lucide-react";

import IssueCard from "../components/issues/IssueCard";
import IssueForm from "../components/issues/IssueForm";
import IssueDetails from "../components/issues/IssueDetails";

export default function Issues() {
  const [issues, setIssues] = useState([]);
  const [filteredIssues, setFilteredIssues] = useState([]);
  const [selectedIssue, setSelectedIssue] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [activeTab, setActiveTab] = useState("open");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadIssues();
  }, []);

  useEffect(() => {
    filterIssues();
  }, [issues, activeTab]);

  const loadIssues = async () => {
    try {
      const data = await Issue.list("-created_date", 100);
      setIssues(data);
    } catch (error) {
      console.error("Error loading issues:", error);
    }
    setIsLoading(false);
  };

  const filterIssues = () => {
    if (activeTab === "all") {
      setFilteredIssues(issues);
    } else {
      setFilteredIssues(issues.filter(issue => issue.status === activeTab));
    }
  };

  const handleIssueCreate = async (issueData) => {
    try {
      const user = await User.me();
      await Issue.create({
        ...issueData,
        responsible_id: user.id
      });
      setShowForm(false);
      loadIssues();
    } catch (error) {
      console.error("Error creating issue:", error);
      alert("Erro ao criar ocorrência. Tente novamente.");
    }
  };

  const handleStatusUpdate = async (issueId, newStatus, resolution = null) => {
    try {
      const updateData = { 
        status: newStatus,
        ...(resolution && { resolution, resolution_date: new Date().toISOString() })
      };
      
      await Issue.update(issueId, updateData);
      loadIssues();
      
      if (selectedIssue && selectedIssue.id === issueId) {
        setSelectedIssue(null);
      }
    } catch (error) {
      console.error("Error updating issue:", error);
      alert("Erro ao atualizar ocorrência. Tente novamente.");
    }
  };

  const getTabCounts = () => {
    return {
      all: issues.length,
      open: issues.filter(i => i.status === 'open').length,
      investigating: issues.filter(i => i.status === 'investigating').length,
      resolved: issues.filter(i => i.status === 'resolved').length
    };
  };

  const tabCounts = getTabCounts();

  const tabs = [
    { value: "open", label: "Abertas", count: tabCounts.open, icon: AlertTriangle, color: "text-red-600" },
    { value: "investigating", label: "Investigando", count: tabCounts.investigating, icon: Clock, color: "text-amber-600" },
    { value: "resolved", label: "Resolvidas", count: tabCounts.resolved, icon: CheckCircle, color: "text-emerald-600" },
    { value: "all", label: "Todas", count: tabCounts.all, icon: Eye, color: "text-slate-600" }
  ];

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-slate-200 rounded w-1/3"></div>
          <div className="grid gap-4">
            {Array(5).fill(0).map((_, i) => (
              <div key={i} className="h-32 bg-slate-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="p-6 md:p-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <AlertTriangle className="w-8 h-8 text-red-500" />
              Gestão de Ocorrências
            </h1>
            <p className="text-slate-600 mt-1">
              Registre, acompanhe e resolva problemas operacionais
            </p>
          </div>
          <Button 
            onClick={() => setShowForm(true)}
            className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white shadow-lg"
          >
            <Plus className="w-4 h-4 mr-2" />
            Nova Ocorrência
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <Card key={tab.value} className="border-none shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-slate-600">{tab.label}</p>
                      <p className="text-2xl font-bold text-slate-900">{tab.count}</p>
                    </div>
                    <div className="p-3 rounded-xl bg-slate-50 border border-slate-200">
                      <Icon className={`w-5 h-5 ${tab.color}`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Issues List */}
          <div className="lg:col-span-2">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-2">
                <TabsList className="grid w-full grid-cols-4 bg-transparent gap-1">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <TabsTrigger 
                        key={tab.value} 
                        value={tab.value}
                        className="relative data-[state=active]:bg-red-50 data-[state=active]:text-red-700 data-[state=active]:border-red-200 border border-transparent rounded-lg px-3 py-2"
                      >
                        <Icon className="w-4 h-4 mr-2" />
                        <span className="text-sm font-medium">{tab.label}</span>
                        {tab.count > 0 && (
                          <Badge 
                            variant="secondary" 
                            className="ml-2 text-xs h-5 w-5 p-0 flex items-center justify-center bg-slate-100 text-slate-700"
                          >
                            {tab.count}
                          </Badge>
                        )}
                      </TabsTrigger>
                    );
                  })}
                </TabsList>
              </div>

              <TabsContent value={activeTab}>
                {filteredIssues.length === 0 ? (
                  <Card className="border-none shadow-lg">
                    <CardContent className="text-center py-12">
                      <AlertTriangle className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-slate-900 mb-2">
                        {activeTab === "open" ? "Nenhuma ocorrência aberta" :
                         activeTab === "investigating" ? "Nenhuma ocorrência em investigação" :
                         activeTab === "resolved" ? "Nenhuma ocorrência resolvida" :
                         "Nenhuma ocorrência encontrada"}
                      </h3>
                      <p className="text-slate-500 mb-6">
                        {activeTab === "open" 
                          ? "Registre problemas operacionais para acompanhamento"
                          : "As ocorrências com este status aparecerão aqui"
                        }
                      </p>
                      {activeTab === "open" && (
                        <Button 
                          onClick={() => setShowForm(true)}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Registrar Ocorrência
                        </Button>
                      )}
                    </CardContent>
                  </Card>
                ) : (
                  <div className="space-y-4">
                    {filteredIssues.map((issue) => (
                      <IssueCard 
                        key={issue.id} 
                        issue={issue}
                        onClick={() => setSelectedIssue(issue)}
                        onStatusUpdate={handleStatusUpdate}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>

          {/* Issue Details Sidebar */}
          <div className="lg:col-span-1">
            <IssueDetails 
              issue={selectedIssue}
              onClose={() => setSelectedIssue(null)}
              onStatusUpdate={handleStatusUpdate}
            />
          </div>
        </div>

        {/* Issue Form Modal */}
        {showForm && (
          <IssueForm 
            onClose={() => setShowForm(false)}
            onSubmit={handleIssueCreate}
          />
        )}
      </div>
    </div>
  );
}