{"name": "Product", "type": "object", "properties": {"ean": {"type": "string", "description": "EAN barcode identifier"}, "main_name": {"type": "string", "description": "Primary product name chosen by manager"}, "category": {"type": "string", "description": "Product category"}, "standard_unit": {"type": "string", "description": "Standard unit of measure"}, "active": {"type": "boolean", "default": true, "description": "Product is active"}, "total_variations": {"type": "number", "default": 1, "description": "Number of name variations"}, "total_suppliers": {"type": "number", "default": 1, "description": "Number of different suppliers"}, "total_entries": {"type": "number", "default": 0, "description": "Total number of entries"}, "average_price": {"type": "number", "description": "Average price"}, "min_price": {"type": "number", "description": "Minimum price"}, "max_price": {"type": "number", "description": "Maximum price"}, "last_entry": {"type": "string", "format": "date", "description": "Date of last entry"}, "frequency_trend": {"type": "string", "enum": ["growing", "stable", "declining"], "description": "Entry frequency trend"}}, "required": ["ean", "main_name"]}