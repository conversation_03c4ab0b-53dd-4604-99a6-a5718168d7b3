import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Invoice } from "@/entities/all";
import { 
  History, 
  CheckCircle, 
  FileText, 
  Building2,
  Calendar,
  Clock
} from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";

export default function VerificationHistory({ currentUser }) {
  const [verifiedInvoices, setVerifiedInvoices] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadVerificationHistory();
  }, []);

  const loadVerificationHistory = async () => {
    try {
      // Load invoices that have been verified
      const invoices = await Invoice.filter({ 
        status: ["verified", "dispatched", "completed"] 
      }, "-updated_date", 50);
      
      setVerifiedInvoices(invoices);
    } catch (error) {
      console.error("Error loading verification history:", error);
    }
    setIsLoading(false);
  };

  const getStatusConfig = (status) => {
    const configs = {
      verified: {
        label: "Conferida",
        color: "bg-emerald-100 text-emerald-700 border-emerald-200"
      },
      dispatched: {
        label: "Despachada", 
        color: "bg-purple-100 text-purple-700 border-purple-200"
      },
      completed: {
        label: "Finalizada",
        color: "bg-green-100 text-green-700 border-green-200"
      }
    };
    return configs[status] || configs.verified;
  };

  if (isLoading) {
    return (
      <Card className="border-none shadow-lg">
        <CardContent className="p-8">
          <div className="animate-pulse space-y-4">
            {Array(5).fill(0).map((_, i) => (
              <div key={i} className="h-20 bg-slate-200 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-none shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="w-5 h-5 text-indigo-600" />
          Histórico de Conferências ({verifiedInvoices.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {verifiedInvoices.length === 0 ? (
          <div className="text-center py-12">
            <History className="w-16 h-16 text-slate-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-slate-900 mb-2">
              Nenhuma conferência realizada
            </h3>
            <p className="text-slate-500">
              As conferências concluídas aparecerão aqui
            </p>
          </div>
        ) : (
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {verifiedInvoices.map((invoice) => {
              const statusConfig = getStatusConfig(invoice.status);
              
              return (
                <div
                  key={invoice.id}
                  className="p-4 rounded-lg border border-slate-200 bg-white hover:shadow-md transition-all duration-200"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-indigo-100 rounded-lg">
                        <CheckCircle className="w-5 h-5 text-indigo-600" />
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h4 className="font-semibold text-slate-900">
                            NF-e {invoice.invoice_number}
                          </h4>
                          <Badge className={`${statusConfig.color} border`}>
                            {statusConfig.label}
                          </Badge>
                          {invoice.verification_type && (
                            <Badge variant="outline" className="text-xs">
                              {invoice.verification_type === 'simple' ? 'Simples' :
                               invoice.verification_type === 'volume' ? 'Volume' : 'Detalhada'}
                            </Badge>
                          )}
                        </div>
                        
                        <div className="grid md:grid-cols-2 gap-3 text-sm">
                          <div className="flex items-center gap-2">
                            <Building2 className="w-3 h-3 text-slate-500" />
                            <span className="text-slate-600 truncate">
                              {invoice.issuer_name}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="w-3 h-3 text-slate-500" />
                            <span className="text-slate-600">
                              {format(new Date(invoice.updated_date), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
                            </span>
                          </div>
                        </div>

                        {invoice.volume_code && (
                          <div className="flex items-center gap-2 text-sm">
                            <span className="text-slate-500">Volume:</span>
                            <Badge variant="outline" className="text-xs">
                              {invoice.volume_code}
                            </Badge>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="text-right text-sm">
                      <p className="font-medium text-slate-900">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(invoice.total_value)}
                      </p>
                      <p className="text-slate-500 flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {formatDistanceToNow(new Date(invoice.updated_date), {
                          addSuffix: true,
                          locale: ptBR
                        })}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}