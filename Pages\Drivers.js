import React, { useState, useEffect } from "react";
import { Driver } from "@/entities/all";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  Plus, 
  Search,
  Phone,
  DollarSign,
  Calendar,
  User,
  Truck
} from "lucide-react";

import DriverManager from "../components/dispatch/DriverManager";

export default function DriversPage() {
  const [drivers, setDrivers] = useState([]);
  const [filteredDrivers, setFilteredDrivers] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDrivers();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [drivers, searchTerm]);

  const loadDrivers = async () => {
    try {
      const data = await Driver.list("-created_date");
      setDrivers(data);
    } catch (error) {
      console.error("Error loading drivers:", error);
    }
    setIsLoading(false);
  };

  const applyFilters = () => {
    let filtered = drivers;

    if (searchTerm) {
      filtered = filtered.filter(driver => 
        driver.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        driver.phone?.includes(searchTerm) ||
        driver.document?.includes(searchTerm)
      );
    }

    setFilteredDrivers(filtered);
  };

  const getStatsCards = () => {
    const activeDrivers = drivers.filter(d => d.active).length;
    const inactiveDrivers = drivers.filter(d => !d.active).length;
    const dailyFixed = drivers.filter(d => d.payment_model === 'daily_fixed').length;
    const perDelivery = drivers.filter(d => d.payment_model === 'per_delivery').length;

    return [
      {
        title: "Total de Motoristas",
        value: drivers.length,
        icon: Users,
        color: "blue"
      },
      {
        title: "Motoristas Ativos", 
        value: activeDrivers,
        icon: User,
        color: "emerald"
      },
      {
        title: "Diária Fixa",
        value: dailyFixed,
        icon: DollarSign,
        color: "purple"
      },
      {
        title: "Por Entrega",
        value: perDelivery,
        icon: Truck,
        color: "amber"
      }
    ];
  };

  const getColorClasses = (color) => {
    const colors = {
      blue: {
        bg: "bg-blue-500",
        text: "text-blue-600",
        bgLight: "bg-blue-50",
        border: "border-blue-200"
      },
      emerald: {
        bg: "bg-emerald-500",
        text: "text-emerald-600",
        bgLight: "bg-emerald-50",
        border: "border-emerald-200"
      },
      purple: {
        bg: "bg-purple-500",
        text: "text-purple-600",
        bgLight: "bg-purple-50",
        border: "border-purple-200"
      },
      amber: {
        bg: "bg-amber-500",
        text: "text-amber-600",
        bgLight: "bg-amber-50",
        border: "border-amber-200"
      }
    };
    return colors[color];
  };

  const statsCards = getStatsCards();

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-slate-200 rounded w-1/3"></div>
          <div className="grid md:grid-cols-4 gap-4">
            {Array(4).fill(0).map((_, i) => (
              <div key={i} className="h-32 bg-slate-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="p-6 md:p-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <Users className="w-8 h-8 text-indigo-600" />
              Gestão de Motoristas
            </h1>
            <p className="text-slate-600 mt-1">
              Cadastre e gerencie motoristas e entregadores
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statsCards.map((stat, index) => {
            const colors = getColorClasses(stat.color);
            const Icon = stat.icon;
            
            return (
              <Card key={index} className="border-none shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-slate-600 mb-2">
                        {stat.title}
                      </p>
                      <p className="text-2xl font-bold text-slate-900">
                        {stat.value}
                      </p>
                    </div>
                    <div className={`p-3 rounded-xl ${colors.bgLight} ${colors.border} border`}>
                      <Icon className={`w-5 h-5 ${colors.text}`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Search */}
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
            <Input
              placeholder="Buscar por nome, telefone ou documento..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Driver Manager Component */}
        <DriverManager 
          drivers={filteredDrivers}
          onDriverUpdate={loadDrivers}
        />
      </div>
    </div>
  );
}