import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarContent, AvatarFallback } from "@/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import { 
  FileText, 
  CheckCircle, 
  Truck, 
  AlertTriangle,
  Building2,
  Calendar,
  DollarSign
} from "lucide-react";

export default function RecentActivity({ recentInvoices, currentUser }) {
  const getStatusBadge = (status) => {
    const statusConfig = {
      available: { label: "Disponível", variant: "secondary", color: "bg-slate-100 text-slate-700" },
      in_verification: { label: "Em Conferência", variant: "default", color: "bg-blue-100 text-blue-700" },
      paused: { label: "Pausada", variant: "outline", color: "bg-amber-100 text-amber-700" },
      verified: { label: "Confer<PERSON>", variant: "default", color: "bg-emerald-100 text-emerald-700" },
      dispatched: { label: "Despachada", variant: "default", color: "bg-purple-100 text-purple-700" },
      completed: { label: "Finalizada", variant: "default", color: "bg-green-100 text-green-700" },
      issue: { label: "Ocorrência", variant: "destructive", color: "bg-red-100 text-red-700" }
    };
    
    return statusConfig[status] || statusConfig.available;
  };

  const getStatusIcon = (status) => {
    const icons = {
      available: FileText,
      in_verification: CheckCircle,
      verified: CheckCircle,
      dispatched: Truck,
      completed: CheckCircle,
      issue: AlertTriangle
    };
    
    const Icon = icons[status] || FileText;
    return <Icon className="w-4 h-4" />;
  };

  return (
    <Card className="border-none shadow-lg">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-slate-900 flex items-center gap-2">
          <FileText className="w-5 h-5 text-blue-600" />
          Atividade Recente
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {recentInvoices.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-slate-300 mx-auto mb-4" />
              <p className="text-slate-500">Nenhuma atividade recente</p>
            </div>
          ) : (
            recentInvoices.map((invoice) => {
              const statusConfig = getStatusBadge(invoice.status);
              
              return (
                <div 
                  key={invoice.id} 
                  className="flex items-start gap-4 p-4 rounded-xl bg-white border border-slate-100 hover:border-slate-200 transition-all duration-200 hover:shadow-md"
                >
                  <div className={`p-2 rounded-lg ${statusConfig.color}`}>
                    {getStatusIcon(invoice.status)}
                  </div>
                  
                  <div className="flex-1 min-w-0 space-y-2">
                    <div className="flex items-center justify-between">
                      <p className="font-semibold text-slate-900 truncate">
                        NF-e {invoice.invoice_number}
                      </p>
                      <Badge className={statusConfig.color}>
                        {statusConfig.label}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-slate-600">
                      <div className="flex items-center gap-1">
                        <Building2 className="w-3 h-3" />
                        <span className="truncate max-w-32">
                          {invoice.issuer_name}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <DollarSign className="w-3 h-3" />
                        <span className="font-medium">
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(invoice.total_value)}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1 text-xs text-slate-500">
                      <Calendar className="w-3 h-3" />
                      {formatDistanceToNow(new Date(invoice.created_date), {
                        addSuffix: true,
                        locale: ptBR
                      })}
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </CardContent>
    </Card>
  );
}