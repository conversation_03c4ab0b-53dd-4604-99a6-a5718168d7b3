{"name": "Driver", "type": "object", "properties": {"name": {"type": "string", "description": "Driver name"}, "phone": {"type": "string", "description": "Phone number"}, "document": {"type": "string", "description": "Driver license or ID"}, "payment_model": {"type": "string", "enum": ["daily_fixed", "fixed_plus_route", "per_delivery"], "description": "Payment calculation model"}, "daily_rate": {"type": "number", "description": "Daily fixed rate"}, "route_rate": {"type": "number", "description": "Additional rate per route"}, "delivery_rate": {"type": "number", "description": "Rate per delivery"}, "payment_frequency": {"type": "string", "enum": ["daily", "weekly", "monthly"], "default": "monthly", "description": "How often payments are made"}, "active": {"type": "boolean", "default": true, "description": "Driver is active"}}, "required": ["name", "payment_model"]}