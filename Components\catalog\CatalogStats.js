import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { 
  Package, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  BarChart3,
  Star
} from "lucide-react";

export default function CatalogStats({ products }) {
  const stats = {
    total: products.length,
    trending: products.filter(p => p.frequency_trend === 'growing').length,
    declining: products.filter(p => p.frequency_trend === 'declining').length,
    newProducts: products.filter(p => p.total_entries <= 3).length,
    alerts: products.filter(p => 
      p.frequency_trend === 'declining' || 
      p.total_variations > 5 ||
      (p.max_price - p.min_price) > (p.average_price * 0.3)
    ).length,
    avgVariations: products.reduce((acc, p) => acc + p.total_variations, 0) / (products.length || 1)
  };

  const statCards = [
    {
      title: "Total de Produtos",
      value: stats.total,
      icon: Package,
      color: "blue"
    },
    {
      title: "Em Crescimento", 
      value: stats.trending,
      icon: TrendingUp,
      color: "emerald"
    },
    {
      title: "Em Declínio",
      value: stats.declining,
      icon: TrendingDown,
      color: "red"
    },
    {
      title: "Produtos Novos",
      value: stats.newProducts,
      icon: Star,
      color: "purple"
    },
    {
      title: "Alertas Ativos",
      value: stats.alerts,
      icon: AlertTriangle,
      color: "amber"
    },
    {
      title: "Variações Média",
      value: stats.avgVariations.toFixed(1),
      icon: BarChart3,
      color: "indigo"
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: {
        bg: "bg-blue-500",
        text: "text-blue-600",
        bgLight: "bg-blue-50",
        border: "border-blue-200"
      },
      emerald: {
        bg: "bg-emerald-500",
        text: "text-emerald-600",
        bgLight: "bg-emerald-50",
        border: "border-emerald-200"
      },
      red: {
        bg: "bg-red-500",
        text: "text-red-600",
        bgLight: "bg-red-50",
        border: "border-red-200"
      },
      purple: {
        bg: "bg-purple-500",
        text: "text-purple-600",
        bgLight: "bg-purple-50",
        border: "border-purple-200"
      },
      amber: {
        bg: "bg-amber-500",
        text: "text-amber-600",
        bgLight: "bg-amber-50",
        border: "border-amber-200"
      },
      indigo: {
        bg: "bg-indigo-500",
        text: "text-indigo-600",
        bgLight: "bg-indigo-50",
        border: "border-indigo-200"
      }
    };
    return colors[color];
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
      {statCards.map((stat, index) => {
        const colors = getColorClasses(stat.color);
        const Icon = stat.icon;
        
        return (
          <Card key={index} className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-2">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-slate-900">
                    {stat.value}
                  </p>
                </div>
                <div className={`p-3 rounded-xl ${colors.bgLight} ${colors.border} border`}>
                  <Icon className={`w-5 h-5 ${colors.text}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}