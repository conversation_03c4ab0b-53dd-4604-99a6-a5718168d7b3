import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Payment } from "@/entities/all";
import { 
  CreditCard, 
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  User
} from "lucide-react";

export default function PaymentManager({ drivers, routes, onUpdate }) {
  const [payments, setPayments] = useState([]);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [selectedDriver, setSelectedDriver] = useState(null);
  const [paymentData, setPaymentData] = useState({
    reference_period: new Date().toISOString().slice(0, 7), // YYYY-MM
    notes: ''
  });
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    loadPayments();
  }, []);

  const loadPayments = async () => {
    try {
      const data = await Payment.list("-created_date", 50);
      setPayments(data);
    } catch (error) {
      console.error("Error loading payments:", error);
    }
  };

  const calculatePayment = (driver, period) => {
    const driverRoutes = routes.filter(route => {
      const routeDate = new Date(route.departure_date);
      const [year, month] = period.split('-');
      return route.driver_id === driver.id &&
             routeDate.getFullYear() === parseInt(year) &&
             routeDate.getMonth() === parseInt(month) - 1;
    });

    let baseAmount = 0;
    let bonusAmount = 0;
    const deliveriesCount = driverRoutes.reduce((sum, route) => sum + (route.delivered_invoices || 0), 0);
    const routesCount = driverRoutes.length;
    const workingDays = new Set(driverRoutes.map(r => 
      new Date(r.departure_date).toDateString()
    )).size;

    if (driver.payment_model === 'daily_fixed') {
      baseAmount = workingDays * (driver.daily_rate || 0);
    } else if (driver.payment_model === 'per_delivery') {
      baseAmount = deliveriesCount * (driver.delivery_rate || 0);
    } else if (driver.payment_model === 'fixed_plus_route') {
      baseAmount = workingDays * (driver.daily_rate || 0);
      bonusAmount = routesCount * (driver.route_rate || 0);
    }

    return {
      baseAmount,
      bonusAmount,
      totalAmount: baseAmount + bonusAmount,
      deliveriesCount,
      routesCount,
      workingDays
    };
  };

  const handleProcessPayment = async () => {
    if (!selectedDriver) return;
    
    setIsProcessing(true);
    try {
      const calculation = calculatePayment(selectedDriver, paymentData.reference_period);
      
      await Payment.create({
        driver_id: selectedDriver.id,
        payment_type: selectedDriver.payment_frequency,
        reference_period: paymentData.reference_period,
        base_amount: calculation.baseAmount,
        bonus_amount: calculation.bonusAmount,
        total_amount: calculation.totalAmount,
        deliveries_count: calculation.deliveriesCount,
        routes_count: calculation.routesCount,
        working_days: calculation.workingDays,
        notes: paymentData.notes,
        status: 'pending'
      });

      setShowPaymentDialog(false);
      setSelectedDriver(null);
      setPaymentData({
        reference_period: new Date().toISOString().slice(0, 7),
        notes: ''
      });
      loadPayments();
      onUpdate();
      
      alert("Pagamento processado com sucesso!");
    } catch (error) {
      console.error("Error processing payment:", error);
      alert("Erro ao processar pagamento. Tente novamente.");
    }
    setIsProcessing(false);
  };

  const markAsPaid = async (paymentId) => {
    try {
      await Payment.update(paymentId, {
        status: 'paid',
        paid_at: new Date().toISOString()
      });
      loadPayments();
      onUpdate();
    } catch (error) {
      console.error("Error marking payment as paid:", error);
    }
  };

  const getPaymentTypeLabel = (type) => {
    const labels = {
      daily: "Diário",
      weekly: "Semanal",
      monthly: "Mensal",
      per_delivery: "Por Entrega"
    };
    return labels[type] || type;
  };

  const getStatusBadge = (status) => {
    const configs = {
      pending: { label: "Pendente", color: "bg-amber-100 text-amber-700 border-amber-200" },
      paid: { label: "Pago", color: "bg-emerald-100 text-emerald-700 border-emerald-200" },
      cancelled: { label: "Cancelado", color: "bg-red-100 text-red-700 border-red-200" }
    };
    return configs[status] || configs.pending;
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <Card className="border-none shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5 text-emerald-600" />
            Processar Pagamentos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {drivers.map((driver) => {
              const currentPeriod = new Date().toISOString().slice(0, 7);
              const calculation = calculatePayment(driver, currentPeriod);
              const hasPayment = payments.find(p => 
                p.driver_id === driver.id && 
                p.reference_period === currentPeriod
              );

              return (
                <div key={driver.id} className="p-4 border border-slate-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <User className="w-4 h-4 text-slate-500" />
                    <span className="font-medium text-slate-900">{driver.name}</span>
                  </div>
                  <div className="space-y-1 text-sm">
                    <p className="text-slate-600">
                      Valor: {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(calculation.totalAmount)}
                    </p>
                    <p className="text-slate-600">
                      Entregas: {calculation.deliveriesCount} | Dias: {calculation.workingDays}
                    </p>
                  </div>
                  {hasPayment ? (
                    <Badge className={getStatusBadge(hasPayment.status).color}>
                      {getStatusBadge(hasPayment.status).label}
                    </Badge>
                  ) : (
                    <Button
                      size="sm"
                      className="w-full mt-2"
                      onClick={() => {
                        setSelectedDriver(driver);
                        setShowPaymentDialog(true);
                      }}
                    >
                      Processar
                    </Button>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recent Payments */}
      <Card className="border-none shadow-lg">
        <CardHeader>
          <CardTitle>Pagamentos Recentes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {payments.slice(0, 10).map((payment) => {
              const driver = drivers.find(d => d.id === payment.driver_id);
              const statusConfig = getStatusBadge(payment.status);
              
              return (
                <div key={payment.id} className="flex items-center justify-between p-3 border border-slate-200 rounded-lg">
                  <div>
                    <p className="font-medium text-slate-900">{driver?.name}</p>
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                      <Calendar className="w-3 h-3" />
                      <span>{payment.reference_period}</span>
                      <span>•</span>
                      <span>{getPaymentTypeLabel(payment.payment_type)}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-slate-900">
                      {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(payment.total_amount)}
                    </p>
                    <div className="flex items-center gap-2">
                      <Badge className={statusConfig.color}>
                        {statusConfig.label}
                      </Badge>
                      {payment.status === 'pending' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => markAsPaid(payment.id)}
                        >
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Marcar Pago
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Payment Processing Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Processar Pagamento</DialogTitle>
          </DialogHeader>
          
          {selectedDriver && (
            <div className="space-y-4">
              <div className="p-3 bg-slate-50 rounded-lg">
                <p className="font-medium text-slate-900">{selectedDriver.name}</p>
                <p className="text-sm text-slate-600 capitalize">
                  {selectedDriver.payment_model === 'daily_fixed' ? 'Diária Fixa' :
                   selectedDriver.payment_model === 'per_delivery' ? 'Por Entrega' : 'Fixa + Rota'}
                </p>
              </div>

              <div>
                <Label htmlFor="reference_period">Período de Referência</Label>
                <Input
                  id="reference_period"
                  type="month"
                  value={paymentData.reference_period}
                  onChange={(e) => setPaymentData({...paymentData, reference_period: e.target.value})}
                  className="mt-1"
                />
              </div>

              {(() => {
                const calculation = calculatePayment(selectedDriver, paymentData.reference_period);
                return (
                  <div className="space-y-2 p-3 bg-emerald-50 rounded-lg">
                    <div className="flex justify-between text-sm">
                      <span>Valor Base:</span>
                      <span>{new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(calculation.baseAmount)}</span>
                    </div>
                    {calculation.bonusAmount > 0 && (
                      <div className="flex justify-between text-sm">
                        <span>Bônus:</span>
                        <span>{new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(calculation.bonusAmount)}</span>
                      </div>
                    )}
                    <div className="flex justify-between font-bold text-emerald-700 pt-2 border-t border-emerald-200">
                      <span>Total:</span>
                      <span>{new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(calculation.totalAmount)}</span>
                    </div>
                    <div className="text-xs text-emerald-600 pt-1">
                      {calculation.workingDays} dias • {calculation.routesCount} rotas • {calculation.deliveriesCount} entregas
                    </div>
                  </div>
                );
              })()}

              <div>
                <Label htmlFor="notes">Observações</Label>
                <Textarea
                  id="notes"
                  value={paymentData.notes}
                  onChange={(e) => setPaymentData({...paymentData, notes: e.target.value})}
                  placeholder="Observações sobre o pagamento..."
                  className="mt-1"
                />
              </div>

              <div className="flex justify-end gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowPaymentDialog(false)}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleProcessPayment}
                  disabled={isProcessing}
                >
                  {isProcessing ? 'Processando...' : 'Processar Pagamento'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
