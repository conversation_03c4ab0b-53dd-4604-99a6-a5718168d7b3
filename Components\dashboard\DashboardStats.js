import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Package, 
  AlertTriangle, 
  CheckCircle, 
  Truck, 
  Users,
  TrendingUp,
  TrendingDown
} from "lucide-react";

export default function DashboardStats({ data }) {
  const stats = [
    {
      title: "NF-e Hoje",
      value: data.todayInvoices,
      total: data.totalInvoices,
      icon: FileText,
      color: "blue",
      trend: "+12%",
      trendUp: true
    },
    {
      title: "Em Conferência", 
      value: data.inVerification,
      icon: CheckCircle,
      color: "emerald",
      urgent: data.inVerification > 10
    },
    {
      title: "Despachadas",
      value: data.dispatched,
      icon: Truck,
      color: "amber",
      trend: "+8%",
      trendUp: true
    },
    {
      title: "Ocorrências Abertas",
      value: data.issues,
      icon: AlertTriangle,
      color: "red",
      urgent: data.issues > 5
    },
    {
      title: "Catálogo",
      value: data.totalProducts,
      icon: Package,
      color: "purple",
      trend: "+15 novos",
      trendUp: true
    },
    {
      title: "Motoristas Ativos",
      value: data.activeDrivers,
      icon: Users,
      color: "indigo"
    }
  ];

  const getColorClasses = (color, urgent = false) => {
    if (urgent) {
      return {
        bg: "bg-red-500",
        text: "text-red-600",
        bgLight: "bg-red-50",
        border: "border-red-200"
      };
    }
    
    const colors = {
      blue: {
        bg: "bg-blue-500",
        text: "text-blue-600", 
        bgLight: "bg-blue-50",
        border: "border-blue-200"
      },
      emerald: {
        bg: "bg-emerald-500",
        text: "text-emerald-600",
        bgLight: "bg-emerald-50", 
        border: "border-emerald-200"
      },
      amber: {
        bg: "bg-amber-500",
        text: "text-amber-600",
        bgLight: "bg-amber-50",
        border: "border-amber-200"
      },
      red: {
        bg: "bg-red-500",
        text: "text-red-600",
        bgLight: "bg-red-50",
        border: "border-red-200"
      },
      purple: {
        bg: "bg-purple-500", 
        text: "text-purple-600",
        bgLight: "bg-purple-50",
        border: "border-purple-200"
      },
      indigo: {
        bg: "bg-indigo-500",
        text: "text-indigo-600", 
        bgLight: "bg-indigo-50",
        border: "border-indigo-200"
      }
    };
    
    return colors[color];
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
      {stats.map((stat, index) => {
        const colors = getColorClasses(stat.color, stat.urgent);
        
        return (
          <Card 
            key={index} 
            className={`border-none shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 ${
              stat.urgent ? 'ring-2 ring-red-200 animate-pulse' : ''
            }`}
          >
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-slate-600">
                    {stat.title}
                  </p>
                  <div className="flex items-baseline gap-2">
                    <p className="text-3xl font-bold text-slate-900">
                      {stat.value}
                    </p>
                    {stat.total && (
                      <span className="text-sm text-slate-500">
                        / {stat.total}
                      </span>
                    )}
                  </div>
                  {stat.trend && (
                    <div className="flex items-center gap-1">
                      {stat.trendUp ? (
                        <TrendingUp className="w-3 h-3 text-emerald-500" />
                      ) : (
                        <TrendingDown className="w-3 h-3 text-red-500" />
                      )}
                      <span className={`text-xs font-medium ${
                        stat.trendUp ? 'text-emerald-600' : 'text-red-600'
                      }`}>
                        {stat.trend}
                      </span>
                    </div>
                  )}
                </div>
                <div className={`p-3 rounded-xl ${colors.bgLight} ${colors.border} border`}>
                  <stat.icon className={`w-5 h-5 ${colors.text}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}