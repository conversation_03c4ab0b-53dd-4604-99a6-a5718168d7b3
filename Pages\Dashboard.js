import React, { useState, useEffect } from "react";
import { User } from "@/entities/User";
import { Invoice, Product, Issue, Driver } from "@/entities/all";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  FileText, 
  Package, 
  AlertTriangle, 
  CheckCircle, 
  Truck, 
  Users,
  TrendingUp,
  TrendingDown,
  Clock,
  DollarSign
} from "lucide-react";

import DashboardStats from "../components/dashboard/DashboardStats";
import RecentActivity from "../components/dashboard/RecentActivity";
import InvoiceStatusChart from "../components/dashboard/InvoiceStatusChart";
import AlertsPanel from "../components/dashboard/AlertsPanel";

export default function Dashboard() {
  const [currentUser, setCurrentUser] = useState(null);
  const [dashboardData, setDashboardData] = useState({
    totalInvoices: 0,
    todayInvoices: 0,
    inVerification: 0,
    dispatched: 0,
    completed: 0,
    issues: 0,
    totalProducts: 0,
    activeDrivers: 0,
    recentInvoices: [],
    recentIssues: []
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const user = await User.me();
      setCurrentUser(user);

      // Load all data in parallel
      const [invoices, products, issues, drivers] = await Promise.all([
        Invoice.list("-created_date", 50),
        Product.list("-created_date", 20),
        Issue.filter({ status: "open" }, "-created_date", 10),
        Driver.filter({ active: true })
      ]);

      // Calculate stats
      const today = new Date().toDateString();
      const todayInvoices = invoices.filter(inv => 
        new Date(inv.created_date).toDateString() === today
      );

      const statusCounts = invoices.reduce((acc, inv) => {
        acc[inv.status] = (acc[inv.status] || 0) + 1;
        return acc;
      }, {});

      setDashboardData({
        totalInvoices: invoices.length,
        todayInvoices: todayInvoices.length,
        inVerification: statusCounts.in_verification || 0,
        dispatched: statusCounts.dispatched || 0,
        completed: statusCounts.completed || 0,
        issues: issues.length,
        totalProducts: products.length,
        activeDrivers: drivers.length,
        recentInvoices: invoices.slice(0, 10),
        recentIssues: issues
      });
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    }
    setIsLoading(false);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Bom dia";
    if (hour < 18) return "Boa tarde";
    return "Boa noite";
  };

  if (isLoading) {
    return (
      <div className="p-8 space-y-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-slate-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array(4).fill(0).map((_, i) => (
              <div key={i} className="h-32 bg-slate-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="p-6 md:p-8 max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">
              {getGreeting()}, {currentUser?.full_name?.split(' ')[0] || 'Usuário'}!
            </h1>
            <p className="text-slate-600 mt-1">
              Acompanhe o status das operações em tempo real
            </p>
          </div>
          <div className="flex items-center gap-2 text-sm text-slate-500">
            <Clock className="w-4 h-4" />
            Atualizado agora
          </div>
        </div>

        {/* Stats Grid */}
        <DashboardStats data={dashboardData} />

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Left Column - Charts and Activity */}
          <div className="lg:col-span-2 space-y-6">
            <InvoiceStatusChart data={dashboardData} />
            <RecentActivity 
              recentInvoices={dashboardData.recentInvoices}
              currentUser={currentUser}
            />
          </div>

          {/* Right Column - Alerts and Quick Info */}
          <div className="space-y-6">
            <AlertsPanel issues={dashboardData.recentIssues} />
            
            {/* Quick Performance Metrics */}
            <Card className="border-none shadow-lg">
              <CardHeader>
                <CardTitle className="text-lg font-bold text-slate-900">
                  Performance Hoje
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-slate-600">Eficiência Conferência</span>
                    <span className="font-semibold text-emerald-600">94%</span>
                  </div>
                  <Progress value={94} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-slate-600">Taxa de Entrega</span>
                    <span className="font-semibold text-blue-600">87%</span>
                  </div>
                  <Progress value={87} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-slate-600">Produtos Catalogados</span>
                    <span className="font-semibold text-purple-600">78%</span>
                  </div>
                  <Progress value={78} className="h-2" />
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions for different roles */}
            {currentUser?.role === 'manager' && (
              <Card className="border-none shadow-lg bg-gradient-to-r from-blue-50 to-emerald-50 border-l-4 border-l-blue-500">
                <CardHeader>
                  <CardTitle className="text-lg font-bold text-slate-900">
                    Ações Rápidas - Gestor
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-white/60 rounded-lg">
                      <span className="text-sm font-medium">Relatório Diário</span>
                      <TrendingUp className="w-4 h-4 text-emerald-600" />
                    </div>
                    <div className="flex items-center justify-between p-3 bg-white/60 rounded-lg">
                      <span className="text-sm font-medium">Revisar Ocorrências</span>
                      <AlertTriangle className="w-4 h-4 text-amber-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}