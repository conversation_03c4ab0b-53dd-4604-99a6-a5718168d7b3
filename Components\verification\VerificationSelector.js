import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Building2, 
  DollarSign, 
  Calendar,
  Play,
  Package,
  Layers,
  Grid3X3
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

export default function VerificationSelector({ invoices, currentUser, onStartVerification }) {
  const [selectedInvoice, setSelectedInvoice] = React.useState(null);
  const [verificationType, setVerificationType] = React.useState('simple');

  const verificationTypes = [
    {
      type: 'simple',
      title: 'Conferência Simples',
      description: 'Apenas conferência de quantidades',
      icon: FileText,
      features: ['Conferir quantidades', 'Sem códigos de volume', 'Processo rápido']
    },
    {
      type: 'volume',
      title: 'Conferência por Volume',
      description: 'Um código único para toda a NF-e',
      icon: Package,
      features: ['Código único (A, B, C...)', 'Etiquetas de volume', 'Organização física']
    },
    {
      type: 'detailed',
      title: 'Conferência Detalhada',
      description: 'Produtos distribuídos por caixas numeradas',
      icon: Grid3X3,
      features: ['Códigos por caixa (A1, A2...)', 'Controle detalhado', 'Máxima precisão']
    }
  ];

  const handleStart = () => {
    if (selectedInvoice) {
      onStartVerification(selectedInvoice, verificationType);
    }
  };

  const canStart = currentUser?.role === 'verifier' || currentUser?.role === 'manager' || currentUser?.role === 'admin';

  return (
    <div className="grid lg:grid-cols-3 gap-8">
      {/* Invoice Selection */}
      <div className="lg:col-span-2 space-y-4">
        <Card className="border-none shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-blue-600" />
              NF-e Disponíveis para Conferência ({invoices.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {invoices.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                <p className="text-slate-500">Nenhuma NF-e disponível para conferência</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {invoices.map((invoice) => (
                  <div
                    key={invoice.id}
                    onClick={() => setSelectedInvoice(invoice)}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                      selectedInvoice?.id === invoice.id 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-slate-200 hover:border-slate-300 bg-white'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-bold text-slate-900">
                            NF-e {invoice.invoice_number}
                          </h3>
                          <Badge 
                            className={invoice.status === 'paused' 
                              ? 'bg-amber-100 text-amber-700 border-amber-200' 
                              : 'bg-slate-100 text-slate-700 border-slate-200'
                            }
                          >
                            {invoice.status === 'paused' ? 'Pausada' : 'Disponível'}
                          </Badge>
                        </div>
                        
                        <div className="grid md:grid-cols-3 gap-3 text-sm">
                          <div className="flex items-center gap-2">
                            <Building2 className="w-3 h-3 text-slate-500" />
                            <span className="text-slate-600 truncate">
                              {invoice.issuer_name}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <DollarSign className="w-3 h-3 text-slate-500" />
                            <span className="font-medium text-slate-900">
                              {new Intl.NumberFormat('pt-BR', {
                                style: 'currency',
                                currency: 'BRL'
                              }).format(invoice.total_value)}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="w-3 h-3 text-slate-500" />
                            <span className="text-slate-600">
                              {format(new Date(invoice.created_date), "dd/MM/yyyy", { locale: ptBR })}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      {selectedInvoice?.id === invoice.id && (
                        <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Verification Type Selection */}
      <div className="space-y-6">
        <Card className="border-none shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Layers className="w-5 h-5 text-emerald-600" />
              Tipo de Conferência
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {verificationTypes.map((type) => {
              const Icon = type.icon;
              return (
                <div
                  key={type.type}
                  onClick={() => setVerificationType(type.type)}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                    verificationType === type.type 
                      ? 'border-emerald-500 bg-emerald-50' 
                      : 'border-slate-200 hover:border-slate-300 bg-white'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-lg ${
                      verificationType === type.type 
                        ? 'bg-emerald-100 border border-emerald-200' 
                        : 'bg-slate-100 border border-slate-200'
                    }`}>
                      <Icon className={`w-4 h-4 ${
                        verificationType === type.type ? 'text-emerald-600' : 'text-slate-500'
                      }`} />
                    </div>
                    
                    <div className="space-y-2">
                      <div>
                        <h4 className="font-semibold text-slate-900">{type.title}</h4>
                        <p className="text-sm text-slate-600">{type.description}</p>
                      </div>
                      
                      <ul className="text-xs text-slate-500 space-y-1">
                        {type.features.map((feature, index) => (
                          <li key={index} className="flex items-center gap-1">
                            <span className="w-1 h-1 bg-slate-400 rounded-full"></span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              );
            })}
          </CardContent>
        </Card>

        {/* Start Button */}
        <Button
          onClick={handleStart}
          disabled={!selectedInvoice || !canStart}
          className="w-full bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 text-white shadow-lg h-12"
        >
          <Play className="w-4 h-4 mr-2" />
          Iniciar Conferência
        </Button>

        {!canStart && (
          <p className="text-xs text-center text-slate-500">
            Apenas conferentes podem iniciar conferências
          </p>
        )}
      </div>
    </div>
  );
}