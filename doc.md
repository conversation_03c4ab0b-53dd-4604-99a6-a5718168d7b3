# Sistema de Conferência e Expedição de Pedidos - Documentação Técnica Completa
## Especificação para Desenvolvimento SaaS Multi-Tenant

---

## 📋 Índice

1. [Visão Geral do Sistema](#visão-geral)
2. [Arquitetura Tecnológica](#arquitetura)
3. [Stack Tecnológica](#stack)
4. [Modelo de Dados](#modelo-dados)
5. [Regras de Negócio](#regras-negocio)
6. [Funcionalidades Detalhadas](#funcionalidades)
7. [Interface e UX](#interface-ux)
8. [Segurança](#seguranca)
9. [Testes](#testes)
10. [Deploy e DevOps](#deploy)
11. [Performance e Escalabilidade](#performance)
12. [Monitoramento](#monitoramento)

---

## 🎯 Visão Geral do Sistema {#visao-geral}

### Objetivo
Sistema SaaS multi-tenant para automatizar e agilizar o processo de entrada, conferência e expedição de pedidos através da leitura de notas fiscais eletrônicas (NF-e), com foco em eficiência operacional, precisão na conferência e flexibilidade para diferentes tipos de clientes.

### Características Principais
- **Multi-tenant**: Isolamento completo entre organizações
- **Tempo Real**: Atualizações instantâneas via WebSockets
- **Mobile First**: Interface responsiva otimizada para tablets e smartphones
- **Scanner Integrado**: Leitura de códigos de barras nativa
- **Catálogo Inteligente**: Sistema automático com análise de tendências
- **Dashboard Analytics**: Métricas e KPIs em tempo real
- **Sistema Financeiro**: Controle completo de pagamentos e repasses

### Personas e Casos de Uso
- **Supermercados**: Conferência rápida, expedição organizada
- **Galpões de Distribuição**: Controle detalhado, rastreabilidade completa
- **Centros de Redistribuição**: Gestão de múltiplos fornecedores

---

## 🏗️ Arquitetura Tecnológica {#arquitetura}

### Arquitetura Geral
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (SvelteKit)   │◄──►│   (SvelteKit)   │◄──►│   (Supabase)    │
│                 │    │   API Routes    │    │   PostgreSQL    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   Supabase      │              │
         └──────────────►│   Auth & RT     │◄─────────────┘
                        │   Storage       │
                        └─────────────────┘
```

### Padrões Arquiteturais
- **MVC Pattern**: Model-View-Controller para organização do código
- **Repository Pattern**: Abstração da camada de dados
- **Service Layer**: Lógica de negócio centralizada
- **Event-Driven**: Comunicação via eventos para desacoplamento
- **CQRS**: Separação de comandos e consultas para performance

### Estrutura de Pastas
```
src/
├── lib/
│   ├── components/          # Componentes reutilizáveis
│   │   ├── ui/             # Componentes base (Button, Card, etc.)
│   │   ├── dashboard/      # Componentes do dashboard
│   │   ├── invoices/       # Componentes de NF-e
│   │   ├── catalog/        # Componentes do catálogo
│   │   ├── verification/   # Componentes de conferência
│   │   ├── dispatch/       # Componentes de expedição
│   │   └── shared/         # Componentes compartilhados
│   ├── stores/             # Svelte stores (estado global)
│   ├── services/           # Serviços de API
│   ├── utils/              # Utilitários e helpers
│   ├── types/              # Definições TypeScript
│   └── constants/          # Constantes da aplicação
├── routes/
│   ├── (app)/             # Rotas da aplicação
│   │   ├── dashboard/
│   │   ├── invoices/
│   │   ├── catalog/
│   │   ├── verification/
│   │   ├── dispatch/
│   │   ├── issues/
│   │   ├── drivers/
│   │   ├── financial/
│   │   └── settings/
│   ├── api/               # API routes
│   │   ├── invoices/
│   │   ├── products/
│   │   ├── verifications/
│   │   └── auth/
│   └── auth/              # Páginas de autenticação
├── app.html               # Template HTML principal
└── hooks.server.js        # Hooks do servidor
```

---

## 🛠️ Stack Tecnológica {#stack}

### Frontend
- **SvelteKit 2.0**: Framework principal com SSR/SPA
- **TypeScript**: Tipagem estática para maior robustez
- **TailwindCSS 3.4**: Estilização utilitária com design system
- **Lucide Svelte**: Biblioteca de ícones moderna
- **Chart.js**: Gráficos e visualizações
- **Date-fns**: Manipulação de datas
- **Zod**: Validação de schemas

### Backend
- **SvelteKit API Routes**: Endpoints RESTful
- **Supabase Client**: Cliente oficial para integração
- **Node.js**: Runtime JavaScript
- **Prisma**: ORM para type-safety adicional (opcional)

### Banco de Dados
- **Supabase (PostgreSQL)**: Banco principal com recursos avançados
- **Row Level Security (RLS)**: Isolamento multi-tenant
- **Real-time Subscriptions**: Atualizações em tempo real
- **Storage**: Armazenamento de arquivos XML

### Infraestrutura
- **Vercel**: Deploy do frontend
- **Supabase Cloud**: Backend-as-a-Service
- **Vercel Analytics**: Monitoramento de performance
- **Sentry**: Monitoramento de erros

### Ferramentas de Desenvolvimento
- **Vite**: Build tool e dev server
- **ESLint + Prettier**: Linting e formatação
- **Playwright**: Testes E2E
- **Vitest**: Testes unitários
- **Storybook**: Documentação de componentes
- **Husky**: Git hooks para qualidade

---

## 📊 Modelo de Dados {#modelo-dados}

### Estrutura Multi-Tenant

#### 1. Organizações (Tenant Principal)
```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  cnpj VARCHAR(14) UNIQUE,
  plan VARCHAR(50) DEFAULT 'basic',
  status VARCHAR(20) DEFAULT 'active',
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX idx_organizations_cnpj ON organizations(cnpj);
CREATE INDEX idx_organizations_status ON organizations(status);
```

#### 2. Usuários com RLS
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'manager', 'operator', 'verifier')),
  active BOOLEAN DEFAULT true,
  last_login TIMESTAMP,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- RLS Policy
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can only see their organization" ON users
  FOR ALL USING (organization_id = current_setting('app.current_organization_id')::UUID);
```

#### 3. Notas Fiscais (Core Entity)
```sql
CREATE TABLE invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  access_key VARCHAR(44) NOT NULL,
  invoice_number VARCHAR(20) NOT NULL,
  issuer_name VARCHAR(255) NOT NULL,
  issuer_cnpj VARCHAR(14) NOT NULL,
  recipient_name VARCHAR(255) NOT NULL,
  recipient_cnpj VARCHAR(14),
  total_value DECIMAL(12,2) NOT NULL,
  xml_content TEXT NOT NULL,
  xml_file_url TEXT,
  status VARCHAR(20) DEFAULT 'available' CHECK (
    status IN ('available', 'in_verification', 'paused', 'verified', 'dispatched', 'completed', 'issue')
  ),
  volume_code VARCHAR(5),
  verification_type VARCHAR(20) CHECK (
    verification_type IN ('simple', 'volume', 'detailed')
  ),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(organization_id, access_key)
);

-- Índices críticos para performance
CREATE INDEX idx_invoices_org_status ON invoices(organization_id, status);
CREATE INDEX idx_invoices_org_created ON invoices(organization_id, created_at DESC);
CREATE INDEX idx_invoices_access_key ON invoices(access_key);
CREATE INDEX idx_invoices_volume_code ON invoices(organization_id, volume_code) WHERE volume_code IS NOT NULL;

-- RLS Policy
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Invoices isolation by organization" ON invoices
  FOR ALL USING (organization_id = current_setting('app.current_organization_id')::UUID);
```

#### 4. Itens da Nota Fiscal
```sql
CREATE TABLE invoice_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  invoice_id UUID REFERENCES invoices(id) ON DELETE CASCADE,
  product_code VARCHAR(100) NOT NULL,
  description VARCHAR(500) NOT NULL,
  unit VARCHAR(10) NOT NULL,
  quantity_invoice DECIMAL(10,3) NOT NULL,
  unit_price DECIMAL(10,4) NOT NULL,
  total_value DECIMAL(12,2) NOT NULL,
  ean VARCHAR(14),
  ncm VARCHAR(8),
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_invoice_items_invoice ON invoice_items(invoice_id);
CREATE INDEX idx_invoice_items_ean ON invoice_items(ean) WHERE ean IS NOT NULL;
```

#### 5. Sistema de Catálogo Inteligente
```sql
-- Produtos principais (por EAN único)
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  ean VARCHAR(14) NOT NULL,
  main_name VARCHAR(500),
  category VARCHAR(100),
  standard_unit VARCHAR(10),
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(organization_id, ean)
);

-- Variações de nomes
CREATE TABLE product_variations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  name_variation VARCHAR(500) NOT NULL,
  supplier_cnpj VARCHAR(14),
  supplier_name VARCHAR(255),
  first_occurrence TIMESTAMP DEFAULT NOW(),
  last_occurrence TIMESTAMP DEFAULT NOW(),
  total_occurrences INTEGER DEFAULT 1,
  
  UNIQUE(product_id, name_variation, supplier_cnpj)
);

-- Estatísticas calculadas (cache para performance)
CREATE TABLE product_statistics (
  product_id UUID PRIMARY KEY REFERENCES products(id) ON DELETE CASCADE,
  total_variations INTEGER DEFAULT 0,
  total_suppliers INTEGER DEFAULT 0,
  total_entries INTEGER DEFAULT 0,
  average_price DECIMAL(10,4),
  min_price DECIMAL(10,4),
  max_price DECIMAL(10,4),
  first_entry TIMESTAMP,
  last_entry TIMESTAMP,
  monthly_frequency DECIMAL(10,2),
  days_without_entry INTEGER,
  trend VARCHAR(20) CHECK (trend IN ('growing', 'stable', 'declining')),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Histórico de entradas
CREATE TABLE product_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  invoice_item_id UUID REFERENCES invoice_items(id) NOT NULL,
  name_used VARCHAR(500) NOT NULL,
  supplier_cnpj VARCHAR(14),
  supplier_name VARCHAR(255),
  quantity DECIMAL(10,3) NOT NULL,
  unit_price DECIMAL(10,4) NOT NULL,
  total_value DECIMAL(12,2) NOT NULL,
  verifier_id UUID REFERENCES users(id),
  entry_date TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(invoice_item_id)
);
```

#### 6. Sistema de Conferência
```sql
CREATE TABLE verifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  invoice_id UUID REFERENCES invoices(id) ON DELETE CASCADE,
  verifier_id UUID REFERENCES users(id) NOT NULL,
  verification_type VARCHAR(20) NOT NULL CHECK (
    verification_type IN ('simple', 'volume', 'detailed')
  ),
  volume_code VARCHAR(5),
  status VARCHAR(20) DEFAULT 'in_progress' CHECK (
    status IN ('in_progress', 'paused', 'completed', 'cancelled')
  ),
  version INTEGER DEFAULT 1,
  started_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE verification_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  verification_id UUID REFERENCES verifications(id) ON DELETE CASCADE,
  invoice_item_id UUID REFERENCES invoice_items(id) NOT NULL,
  quantity_verified DECIMAL(10,3) DEFAULT 0,
  box_number INTEGER,
  ean_used VARCHAR(14),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Histórico de reinicializações
CREATE TABLE verification_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  original_verification_id UUID REFERENCES verifications(id) NOT NULL,
  previous_verifier_id UUID REFERENCES users(id) NOT NULL,
  new_verifier_id UUID REFERENCES users(id) NOT NULL,
  previous_version INTEGER NOT NULL,
  reason VARCHAR(500),
  restarted_at TIMESTAMP DEFAULT NOW()
);
```

#### 7. Controle de Códigos de Volume
```sql
CREATE TABLE volume_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  code VARCHAR(5) NOT NULL,
  invoice_id UUID REFERENCES invoices(id),
  used_at TIMESTAMP DEFAULT NOW(),
  released_at TIMESTAMP,
  status VARCHAR(20) DEFAULT 'in_use' CHECK (
    status IN ('in_use', 'available')
  ),
  
  UNIQUE(organization_id, code, status)
);
```

#### 8. Sistema de Expedição
```sql
-- Motoristas/Entregadores
CREATE TABLE drivers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  document VARCHAR(20),
  payment_model VARCHAR(30) NOT NULL CHECK (
    payment_model IN ('daily_fixed', 'fixed_plus_route', 'per_delivery')
  ),
  daily_rate DECIMAL(10,2),
  route_rate DECIMAL(10,2),
  delivery_rate DECIMAL(10,2),
  payment_frequency VARCHAR(20) DEFAULT 'monthly' CHECK (
    payment_frequency IN ('daily', 'weekly', 'biweekly', 'monthly')
  ),
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Rotas de entrega
CREATE TABLE delivery_routes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  driver_id UUID REFERENCES drivers(id) NOT NULL,
  route_number VARCHAR(20) NOT NULL,
  status VARCHAR(20) DEFAULT 'on_route' CHECK (
    status IN ('on_route', 'completed')
  ),
  departure_date TIMESTAMP DEFAULT NOW(),
  return_date TIMESTAMP,
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(organization_id, route_number)
);

-- Relacionamento NF-e com rotas
CREATE TABLE route_invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  route_id UUID REFERENCES delivery_routes(id) ON DELETE CASCADE,
  invoice_id UUID REFERENCES invoices(id) NOT NULL,
  delivery_status VARCHAR(20) DEFAULT 'on_route' CHECK (
    delivery_status IN ('on_route', 'delivered', 'issue')
  ),
  delivery_date TIMESTAMP,
  delivery_notes TEXT,
  
  UNIQUE(route_id, invoice_id)
);
```

#### 9. Sistema de Ocorrências
```sql
CREATE TABLE issues (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  invoice_id UUID REFERENCES invoices(id) NOT NULL,
  origin_type VARCHAR(20) NOT NULL CHECK (
    origin_type IN ('verification', 'dispatch', 'manual', 'catalog')
  ),
  issue_type VARCHAR(50) NOT NULL,
  description TEXT NOT NULL,
  impact_value DECIMAL(12,2),
  responsible_id UUID REFERENCES users(id) NOT NULL,
  status VARCHAR(20) DEFAULT 'open' CHECK (
    status IN ('open', 'investigating', 'resolved', 'cancelled')
  ),
  issue_date TIMESTAMP DEFAULT NOW(),
  resolution_date TIMESTAMP,
  resolution TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### 10. Sistema Financeiro
```sql
-- Controle de pagamentos de motoristas
CREATE TABLE driver_payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  driver_id UUID REFERENCES drivers(id) NOT NULL,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  total_deliveries INTEGER DEFAULT 0,
  daily_amount DECIMAL(12,2) DEFAULT 0,
  route_amount DECIMAL(12,2) DEFAULT 0,
  delivery_amount DECIMAL(12,2) DEFAULT 0,
  total_amount DECIMAL(12,2) NOT NULL,
  advance_amount DECIMAL(12,2) DEFAULT 0,
  net_amount DECIMAL(12,2) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (
    status IN ('pending', 'paid')
  ),
  payment_date TIMESTAMP,
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Boletos extraídos das NF-es (opcional)
CREATE TABLE invoices_boletos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  invoice_id UUID REFERENCES invoices(id) NOT NULL,
  boleto_number VARCHAR(100) NOT NULL,
  amount DECIMAL(12,2) NOT NULL,
  due_date DATE NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (
    status IN ('pending', 'paid', 'overdue', 'cancelled')
  ),
  payment_date DATE,
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(organization_id, boleto_number)
);
```

---

## 📋 Regras de Negócio {#regras-negocio}

### 1. Gestão de Organizações (Multi-Tenancy)

#### Isolamento de Dados
- **Regra RN001**: Cada organização deve ter acesso apenas aos seus próprios dados
- **Implementação**: Row Level Security (RLS) no Supabase
- **Validação**: Middleware que injeta `organization_id` em todas as queries

#### Planos e Limites
```typescript
interface OrganizationLimits {
  basic: {
    maxUsers: 5,
    maxInvoicesPerMonth: 1000,
    maxStorageGB: 1
  },
  professional: {
    maxUsers: 20,
    maxInvoicesPerMonth: 5000,
    maxStorageGB: 10
  },
  enterprise: {
    maxUsers: -1, // ilimitado
    maxInvoicesPerMonth: -1,
    maxStorageGB: 100
  }
}
```

### 2. Sistema de Usuários e Permissões

#### Hierarquia de Roles
```typescript
enum UserRole {
  ADMIN = 'admin',      // Acesso total + configurações
  MANAGER = 'manager',  // Todos os fluxos + relatórios
  OPERATOR = 'operator', // Entrada + expedição
  VERIFIER = 'verifier' // Apenas conferência
}

interface RolePermissions {
  [UserRole.ADMIN]: ['*'],
  [UserRole.MANAGER]: [
    'dashboard:read', 'invoices:*', 'catalog:*',
    'verification:read', 'dispatch:*', 'issues:*',
    'drivers:*', 'financial:*', 'settings:read'
  ],
  [UserRole.OPERATOR]: [
    'dashboard:read', 'invoices:create', 'invoices:read',
    'catalog:read', 'dispatch:*', 'issues:create'
  ],
  [UserRole.VERIFIER]: [
    'dashboard:read', 'invoices:read', 'verification:*', 'issues:create'
  ]
}
```

### 3. Estados e Transições de NF-e

#### Máquina de Estados
```typescript
enum InvoiceStatus {
  AVAILABLE = 'available',
  IN_VERIFICATION = 'in_verification',
  PAUSED = 'paused',
  VERIFIED = 'verified',
  DISPATCHED = 'dispatched',
  COMPLETED = 'completed',
  ISSUE = 'issue'
}

const statusTransitions = {
  [InvoiceStatus.AVAILABLE]: [InvoiceStatus.IN_VERIFICATION],
  [InvoiceStatus.IN_VERIFICATION]: [InvoiceStatus.PAUSED, InvoiceStatus.VERIFIED, InvoiceStatus.ISSUE],
  [InvoiceStatus.PAUSED]: [InvoiceStatus.IN_VERIFICATION, InvoiceStatus.AVAILABLE],
  [InvoiceStatus.VERIFIED]: [InvoiceStatus.DISPATCHED, InvoiceStatus.IN_VERIFICATION],
  [InvoiceStatus.DISPATCHED]: [InvoiceStatus.COMPLETED, InvoiceStatus.ISSUE],
  [InvoiceStatus.COMPLETED]: [], // Estado final
  [InvoiceStatus.ISSUE]: [InvoiceStatus.AVAILABLE, InvoiceStatus.VERIFIED]
};
```

### 4. Sistema de Códigos de Volume

#### Geração Automática
- **Regra RN002**: Códigos seguem sequência A-Z, depois AA-ZZ
- **Regra RN003**: Códigos só podem ser reutilizados após NF-e finalizada
- **Regra RN004**: Máximo de 702 códigos simultâneos por organização (26 + 676)

```typescript
class VolumeCodeGenerator {
  private generateNextCode(usedCodes: string[]): string {
    const singleLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');

    // Primeiro tenta códigos simples (A-Z)
    for (const letter of singleLetters) {
      if (!usedCodes.includes(letter)) return letter;
    }

    // Depois códigos duplos (AA-ZZ)
    for (const first of singleLetters) {
      for (const second of singleLetters) {
        const code = first + second;
        if (!usedCodes.includes(code)) return code;
      }
    }

    throw new Error('Limite de códigos de volume atingido');
  }
}
```

### 5. Catálogo Inteligente

#### Processamento Automático
- **Regra RN005**: Todo item de NF-e deve alimentar o catálogo automaticamente
- **Regra RN006**: EAN é chave única por organização
- **Regra RN007**: Variações de nome são registradas por fornecedor

#### Cálculo de Estatísticas
```typescript
interface ProductStatistics {
  calculateTrend(entries: ProductEntry[]): 'growing' | 'stable' | 'declining' {
    const last30Days = entries.filter(e =>
      isAfter(e.entryDate, subDays(new Date(), 30))
    );
    const previous30Days = entries.filter(e =>
      isAfter(e.entryDate, subDays(new Date(), 60)) &&
      isBefore(e.entryDate, subDays(new Date(), 30))
    );

    if (last30Days.length > previous30Days.length * 1.2) return 'growing';
    if (last30Days.length < previous30Days.length * 0.8) return 'declining';
    return 'stable';
  }
}
```

#### Sistema de Alertas
- **Alerta A001**: Produto novo (primeira ocorrência)
- **Alerta A002**: Variação de preço > 30% da média
- **Alerta A003**: Ausência > 30 dias
- **Alerta A004**: Mais de 5 variações de nome
- **Alerta A005**: Fornecedor novo para produto existente

### 6. Sistema de Conferência

#### Tipos de Conferência
```typescript
enum VerificationType {
  SIMPLE = 'simple',     // Apenas quantidades
  VOLUME = 'volume',     // Um código para toda NF-e
  DETAILED = 'detailed'  // Códigos por caixa (A1, A2, A3...)
}

interface VerificationRules {
  [VerificationType.SIMPLE]: {
    generateVolumeCode: false,
    allowBoxNumbers: false,
    requireEanScan: false
  },
  [VerificationType.VOLUME]: {
    generateVolumeCode: true,
    allowBoxNumbers: false,
    requireEanScan: true
  },
  [VerificationType.DETAILED]: {
    generateVolumeCode: true,
    allowBoxNumbers: true,
    requireEanScan: true
  }
}
```

#### Validações de Conferência
- **Regra RN008**: Quantidade conferida não pode ser negativa
- **Regra RN009**: Divergências > 10% geram alerta automático
- **Regra RN010**: Scanner deve validar EAN contra catálogo
- **Regra RN011**: Conferência pausada expira em 24h

### 7. Sistema de Expedição

#### Regras de Criação de Rotas
- **Regra RN012**: Apenas NF-e com status 'verified' podem ser despachadas
- **Regra RN013**: Motorista não pode ter mais de uma rota ativa
- **Regra RN014**: Número de rota deve ser único por organização

#### Cálculo de Pagamentos
```typescript
interface PaymentCalculator {
  calculateDriverPayment(driver: Driver, period: DateRange): PaymentAmount {
    const routes = getRoutesInPeriod(driver.id, period);
    const workingDays = getUniqueWorkingDays(routes);
    const totalDeliveries = getTotalDeliveries(routes);

    switch (driver.paymentModel) {
      case 'daily_fixed':
        return workingDays * driver.dailyRate;

      case 'per_delivery':
        return totalDeliveries * driver.deliveryRate;

      case 'fixed_plus_route':
        return (workingDays * driver.dailyRate) + (routes.length * driver.routeRate);
    }
  }
}
```

### 8. Sistema de Ocorrências

#### Workflow de Status
```typescript
enum IssueStatus {
  OPEN = 'open',
  INVESTIGATING = 'investigating',
  RESOLVED = 'resolved',
  CANCELLED = 'cancelled'
}

const issueWorkflow = {
  [IssueStatus.OPEN]: [IssueStatus.INVESTIGATING, IssueStatus.CANCELLED],
  [IssueStatus.INVESTIGATING]: [IssueStatus.RESOLVED, IssueStatus.OPEN],
  [IssueStatus.RESOLVED]: [], // Estado final
  [IssueStatus.CANCELLED]: [IssueStatus.OPEN]
};
```

#### Tipos de Ocorrências
- **Verificação**: Divergências, produtos danificados, falta de produtos
- **Expedição**: Endereço incorreto, cliente ausente, recusa de entrega
- **Catálogo**: Produto suspeito, variação inconsistente
- **Manual**: Criadas pelo usuário

---

## 🎯 Funcionalidades Detalhadas {#funcionalidades}

### 1. Dashboard Inteligente

#### Métricas Principais
```typescript
interface DashboardMetrics {
  // Métricas de NF-e
  totalInvoices: number;
  todayInvoices: number;
  invoicesByStatus: Record<InvoiceStatus, number>;

  // Métricas de Conferência
  verificationEfficiency: number; // % de conferências sem divergência
  averageVerificationTime: number; // em minutos

  // Métricas de Expedição
  deliveryRate: number; // % de entregas bem-sucedidas
  averageDeliveryTime: number; // em horas

  // Métricas do Catálogo
  totalProducts: number;
  newProductsToday: number;
  productsWithAlerts: number;

  // Métricas Financeiras
  pendingPayments: number;
  monthlyRevenue: number;
}
```

#### Componentes do Dashboard
- **StatsCards**: Cards com métricas principais e tendências
- **ActivityTimeline**: Timeline de atividades recentes
- **StatusChart**: Gráfico de distribuição de status
- **AlertsPanel**: Painel de alertas críticos
- **QuickActions**: Ações rápidas baseadas no role do usuário

### 2. Gestão de NF-e

#### Upload e Processamento
```typescript
interface InvoiceUploadService {
  // Upload por arquivo XML
  uploadXmlFiles(files: File[]): Promise<ProcessingResult[]>;

  // Download por chave de acesso
  downloadByAccessKey(accessKeys: string[]): Promise<ProcessingResult[]>;

  // Processamento do XML
  parseXmlContent(xmlContent: string): Promise<InvoiceData>;

  // Validações
  validateAccessKey(key: string): boolean;
  validateXmlStructure(xml: string): ValidationResult;
}
```

#### Interface de Upload
- **Drag & Drop**: Área de upload com feedback visual
- **Múltiplos Arquivos**: Suporte a upload em lote
- **Progress Tracking**: Barra de progresso em tempo real
- **Validação**: Validação de chave de acesso (44 dígitos)
- **Error Handling**: Tratamento robusto de erros

#### Filtros e Busca
- **Busca Textual**: Por número, emitente, chave de acesso
- **Filtros por Status**: Tabs com contadores em tempo real
- **Filtros por Data**: Range de datas personalizável
- **Filtros por Valor**: Faixa de valores
- **Exportação**: PDF/Excel dos resultados filtrados

### 3. Catálogo Inteligente

#### Interface Principal
```svelte
<!-- Exemplo de componente ProductCard -->
<script lang="ts">
  export let product: Product;
  export let onClick: (product: Product) => void;

  $: hasAlerts = product.statistics.trend === 'declining' ||
                 product.statistics.totalVariations > 5 ||
                 (product.statistics.maxPrice - product.statistics.minPrice) >
                 (product.statistics.averagePrice * 0.3);
</script>

<div class="product-card" class:has-alerts={hasAlerts} on:click={() => onClick(product)}>
  <div class="product-header">
    <div class="product-icon">
      {#if hasAlerts}
        <AlertTriangle class="text-red-500" />
      {:else}
        <Package class="text-purple-500" />
      {/if}
    </div>
    <h3 class="product-name">{product.mainName}</h3>
    <span class="product-ean">EAN: {product.ean}</span>
  </div>

  <div class="product-stats">
    <div class="stat">
      <span class="stat-label">Variações</span>
      <span class="stat-value">{product.statistics.totalVariations}</span>
    </div>
    <div class="stat">
      <span class="stat-label">Fornecedores</span>
      <span class="stat-value">{product.statistics.totalSuppliers}</span>
    </div>
  </div>

  <div class="product-trend">
    <TrendIcon trend={product.statistics.trend} />
    <span class="trend-label">{getTrendLabel(product.statistics.trend)}</span>
  </div>
</div>
```

#### Funcionalidades Avançadas
- **Tabs Inteligentes**: Todos, Em Alta, Novos, Alertas
- **Busca Semântica**: Busca por nome, EAN, categoria
- **Filtros Dinâmicos**: Por categoria, tendência, faixa de preço
- **Sidebar de Detalhes**: Histórico completo, variações, estatísticas
- **Alertas Automáticos**: Sistema de notificações inteligentes

### 4. Sistema de Conferência

#### Interface de Conferência
```svelte
<!-- VerificationInterface.svelte -->
<script lang="ts">
  import { onMount } from 'svelte';
  import { writable } from 'svelte/store';

  export let verification: Verification;
  export let items: InvoiceItem[];

  const scanMode = writable(false);
  const scanInput = writable('');
  const verifiedItems = writable(new Map());

  function handleScan(ean: string) {
    const item = items.find(i => i.ean === ean);
    if (item) {
      verifiedItems.update(map => {
        const current = map.get(item.id) || 0;
        map.set(item.id, current + 1);
        return map;
      });
    }
  }

  function calculateProgress() {
    const totalItems = items.length;
    const verifiedCount = Array.from($verifiedItems.values())
      .filter(qty => qty > 0).length;
    return Math.round((verifiedCount / totalItems) * 100);
  }
</script>

<div class="verification-interface">
  <div class="scanner-section">
    <div class="mode-toggle">
      <button class:active={!$scanMode} on:click={() => scanMode.set(false)}>
        Manual
      </button>
      <button class:active={$scanMode} on:click={() => scanMode.set(true)}>
        Scanner
      </button>
    </div>

    {#if $scanMode}
      <div class="scanner-input">
        <input
          bind:value={$scanInput}
          placeholder="Escaneie ou digite o EAN..."
          on:keydown={(e) => e.key === 'Enter' && handleScan($scanInput)}
        />
      </div>
    {/if}

    <div class="progress-summary">
      <div class="progress-circle">
        <span class="progress-value">{calculateProgress()}%</span>
      </div>
      <div class="progress-stats">
        <div class="stat correct">{getCorrectCount()} Corretos</div>
        <div class="stat divergent">{getDivergentCount()} Divergentes</div>
        <div class="stat pending">{getPendingCount()} Pendentes</div>
      </div>
    </div>
  </div>

  <div class="items-section">
    {#each items as item}
      <VerificationItem
        {item}
        verified={$verifiedItems.get(item.id) || 0}
        on:update={(e) => updateQuantity(item.id, e.detail.quantity)}
      />
    {/each}
  </div>
</div>
```

#### Funcionalidades de Conferência
- **Modo Scanner**: Toggle entre manual e scanner
- **Progress Tracking**: Progresso em tempo real
- **Validação EAN**: Busca automática no catálogo
- **Controle de Quantidades**: Botões +/- e input direto
- **Alertas de Divergência**: Feedback visual imediato
- **Histórico de Versões**: Controle de reinicializações

### 5. Sistema de Expedição

#### Seleção de NF-e para Expedição
```typescript
interface DispatchSelector {
  // Filtrar apenas NF-e verificadas
  getAvailableInvoices(): Promise<Invoice[]>;

  // Agrupar por destinatário
  groupByRecipient(invoices: Invoice[]): Record<string, Invoice[]>;

  // Calcular peso/volume estimado
  calculateRouteMetrics(invoices: Invoice[]): RouteMetrics;

  // Validar capacidade do motorista
  validateDriverCapacity(driver: Driver, invoices: Invoice[]): boolean;
}
```

#### Gestão de Rotas
- **Criação Automática**: Geração de número de rota único
- **Seleção de Motorista**: Lista de motoristas disponíveis
- **Agrupamento Inteligente**: Por região/destinatário
- **Tracking em Tempo Real**: Status de cada entrega
- **Finalização de Rota**: Marcação de entregas/ocorrências

### 6. Sistema Financeiro

#### Dashboard Financeiro
```typescript
interface FinancialDashboard {
  // Métricas principais
  totalPendingPayments: number;
  totalPaidThisMonth: number;
  averagePaymentPerDriver: number;

  // Breakdown por modelo de pagamento
  paymentModelBreakdown: Record<PaymentModel, number>;

  // Projeções
  monthlyProjection: number;
  quarterlyProjection: number;
}
```

#### Cálculo de Pagamentos
- **Automático**: Baseado no modelo de cada motorista
- **Flexível**: Suporte a múltiplos modelos de pagamento
- **Histórico**: Registro completo de todos os pagamentos
- **Relatórios**: Exportação em PDF/Excel

---

## 🎨 Interface e UX {#interface-ux}

### Design System

#### Paleta de Cores
```css
/* Cores principais */
:root {
  /* Primary Colors */
  --color-blue-50: #eff6ff;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;

  /* Success Colors */
  --color-emerald-50: #ecfdf5;
  --color-emerald-500: #10b981;
  --color-emerald-600: #059669;

  /* Warning Colors */
  --color-amber-50: #fffbeb;
  --color-amber-500: #f59e0b;
  --color-amber-600: #d97706;

  /* Error Colors */
  --color-red-50: #fef2f2;
  --color-red-500: #ef4444;
  --color-red-600: #dc2626;

  /* Neutral Colors */
  --color-slate-50: #f8fafc;
  --color-slate-100: #f1f5f9;
  --color-slate-500: #64748b;
  --color-slate-900: #0f172a;
}
```

#### Tipografia
```css
/* Sistema tipográfico */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }

/* Pesos */
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
```

#### Componentes Base
```svelte
<!-- Button.svelte -->
<script lang="ts">
  export let variant: 'primary' | 'secondary' | 'outline' | 'ghost' = 'primary';
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let disabled = false;
  export let loading = false;

  const variants = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-slate-100 hover:bg-slate-200 text-slate-900',
    outline: 'border border-slate-300 hover:bg-slate-50 text-slate-700',
    ghost: 'hover:bg-slate-100 text-slate-700'
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };
</script>

<button
  class="inline-flex items-center justify-center rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed {variants[variant]} {sizes[size]}"
  {disabled}
  on:click
>
  {#if loading}
    <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  {/if}
  <slot />
</button>
```

```svelte
<!-- Card.svelte -->
<script lang="ts">
  export let padding: 'none' | 'sm' | 'md' | 'lg' = 'md';
  export let shadow: 'none' | 'sm' | 'md' | 'lg' = 'md';
  export let hover = false;

  const paddings = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  const shadows = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-lg',
    lg: 'shadow-xl'
  };
</script>

<div
  class="bg-white rounded-xl border border-slate-200 {paddings[padding]} {shadows[shadow]} {hover ? 'hover:shadow-xl transition-shadow duration-300' : ''}"
  on:click
>
  <slot />
</div>
```

### Layout Responsivo

#### Breakpoints
```css
/* Mobile First Approach */
/* xs: 0px - 639px (mobile) */
/* sm: 640px - 767px (large mobile) */
/* md: 768px - 1023px (tablet) */
/* lg: 1024px - 1279px (desktop) */
/* xl: 1280px+ (large desktop) */
```

#### Grid System
```svelte
<!-- ResponsiveGrid.svelte -->
<script lang="ts">
  export let cols: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  } = { xs: 1, sm: 2, md: 3, lg: 4, xl: 6 };

  $: gridClasses = [
    `grid`,
    cols.xs ? `grid-cols-${cols.xs}` : '',
    cols.sm ? `sm:grid-cols-${cols.sm}` : '',
    cols.md ? `md:grid-cols-${cols.md}` : '',
    cols.lg ? `lg:grid-cols-${cols.lg}` : '',
    cols.xl ? `xl:grid-cols-${cols.xl}` : '',
    'gap-6'
  ].filter(Boolean).join(' ');
</script>

<div class={gridClasses}>
  <slot />
</div>
```

### Navegação e Layout

#### Sidebar Responsiva
```svelte
<!-- Sidebar.svelte -->
<script lang="ts">
  import { page } from '$app/stores';
  import { userStore } from '$lib/stores/auth';

  export let isOpen = false;

  const navigationItems = [
    { title: 'Dashboard', href: '/dashboard', icon: 'LayoutDashboard', roles: ['admin', 'manager', 'operator', 'verifier'] },
    { title: 'Notas Fiscais', href: '/invoices', icon: 'FileText', roles: ['admin', 'manager', 'operator', 'verifier'] },
    { title: 'Catálogo', href: '/catalog', icon: 'Package', roles: ['admin', 'manager', 'operator'] },
    { title: 'Conferência', href: '/verification', icon: 'CheckCircle', roles: ['admin', 'manager', 'verifier'] },
    { title: 'Expedição', href: '/dispatch', icon: 'Truck', roles: ['admin', 'manager', 'operator'] },
    { title: 'Financeiro', href: '/financial', icon: 'DollarSign', roles: ['admin', 'manager'] },
    { title: 'Ocorrências', href: '/issues', icon: 'AlertTriangle', roles: ['admin', 'manager', 'operator', 'verifier'] },
    { title: 'Motoristas', href: '/drivers', icon: 'Users', roles: ['admin', 'manager'] }
  ];

  $: filteredItems = navigationItems.filter(item =>
    !$userStore.user || item.roles.includes($userStore.user.role)
  );
</script>

<aside class="sidebar" class:open={isOpen}>
  <div class="sidebar-header">
    <div class="logo">
      <div class="logo-icon">
        <FileText size={20} />
      </div>
      <div class="logo-text">
        <h2>OrderFlow</h2>
        <p>Sistema de Gestão</p>
      </div>
    </div>
  </div>

  <nav class="sidebar-nav">
    <div class="nav-section">
      <h3 class="nav-section-title">Navegação</h3>
      <ul class="nav-list">
        {#each filteredItems as item}
          <li class="nav-item">
            <a
              href={item.href}
              class="nav-link"
              class:active={$page.url.pathname === item.href}
            >
              <svelte:component this={getIcon(item.icon)} size={16} />
              <span>{item.title}</span>
            </a>
          </li>
        {/each}
      </ul>
    </div>
  </nav>

  <div class="sidebar-footer">
    <div class="user-info">
      <div class="user-avatar">
        {$userStore.user?.fullName?.charAt(0) || 'U'}
      </div>
      <div class="user-details">
        <p class="user-name">{$userStore.user?.fullName}</p>
        <p class="user-role">{getRoleLabel($userStore.user?.role)}</p>
      </div>
    </div>
  </div>
</aside>

<style>
  .sidebar {
    @apply fixed left-0 top-0 h-full w-64 bg-white border-r border-slate-200 transform -translate-x-full transition-transform duration-300 z-50;
  }

  .sidebar.open {
    @apply translate-x-0;
  }

  @screen lg {
    .sidebar {
      @apply relative translate-x-0;
    }
  }

  .sidebar-header {
    @apply p-6 border-b border-slate-200;
  }

  .logo {
    @apply flex items-center gap-3;
  }

  .logo-icon {
    @apply w-10 h-10 bg-gradient-to-r from-blue-600 to-emerald-600 rounded-xl flex items-center justify-center text-white;
  }

  .nav-link {
    @apply flex items-center gap-3 px-4 py-3 rounded-xl text-slate-600 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-200;
  }

  .nav-link.active {
    @apply bg-gradient-to-r from-blue-50 to-emerald-50 text-blue-700 border border-blue-100;
  }
</style>
```

### Animações e Micro-interações

#### Transições
```css
/* Transições padrão */
.transition-default {
  transition: all 0.2s ease-in-out;
}

.transition-slow {
  transition: all 0.3s ease-in-out;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-slate-200 rounded;
}

.spinner {
  @apply animate-spin rounded-full border-2 border-slate-200 border-t-blue-600;
}
```

#### Estados de Loading
```svelte
<!-- LoadingState.svelte -->
<script lang="ts">
  export let type: 'skeleton' | 'spinner' | 'dots' = 'skeleton';
  export let size: 'sm' | 'md' | 'lg' = 'md';
</script>

{#if type === 'skeleton'}
  <div class="space-y-4">
    <div class="skeleton h-8 w-1/3"></div>
    <div class="grid gap-4">
      {#each Array(3) as _}
        <div class="skeleton h-24 w-full"></div>
      {/each}
    </div>
  </div>
{:else if type === 'spinner'}
  <div class="flex justify-center items-center p-8">
    <div class="spinner w-8 h-8"></div>
  </div>
{:else if type === 'dots'}
  <div class="flex justify-center items-center p-8 gap-1">
    <div class="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
    <div class="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
    <div class="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
  </div>
{/if}
```

---

## 🔒 Segurança {#seguranca}

### Autenticação e Autorização

#### Supabase Auth Integration
```typescript
// lib/services/auth.ts
import { createClient } from '@supabase/supabase-js';
import type { User, Session } from '@supabase/supabase-js';

export class AuthService {
  private supabase = createClient(
    process.env.PUBLIC_SUPABASE_URL!,
    process.env.PUBLIC_SUPABASE_ANON_KEY!
  );

  async signIn(email: string, password: string) {
    const { data, error } = await this.supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) throw error;

    // Buscar dados adicionais do usuário
    const userData = await this.getUserData(data.user.id);
    return { ...data, userData };
  }

  async signUp(email: string, password: string, organizationId: string, fullName: string) {
    const { data, error } = await this.supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          organization_id: organizationId,
          full_name: fullName
        }
      }
    });

    if (error) throw error;
    return data;
  }

  async signOut() {
    const { error } = await this.supabase.auth.signOut();
    if (error) throw error;
  }

  async getCurrentUser(): Promise<User | null> {
    const { data: { user } } = await this.supabase.auth.getUser();
    return user;
  }

  private async getUserData(userId: string) {
    const { data, error } = await this.supabase
      .from('users')
      .select('*, organizations(*)')
      .eq('id', userId)
      .single();

    if (error) throw error;
    return data;
  }
}
```

#### Row Level Security (RLS)
```sql
-- Política para isolamento multi-tenant
CREATE POLICY "Organization isolation" ON invoices
  FOR ALL USING (
    organization_id = (
      SELECT organization_id
      FROM users
      WHERE id = auth.uid()
    )
  );

-- Política baseada em roles
CREATE POLICY "Role-based access" ON financial_reports
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND organization_id = financial_reports.organization_id
      AND role IN ('admin', 'manager')
    )
  );

-- Política para auditoria
CREATE POLICY "Audit trail" ON audit_logs
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    organization_id = (
      SELECT organization_id
      FROM users
      WHERE id = auth.uid()
    )
  );
```

#### Middleware de Autorização
```typescript
// src/hooks.server.ts
import type { Handle } from '@sveltejs/kit';
import { AuthService } from '$lib/services/auth';

export const handle: Handle = async ({ event, resolve }) => {
  const authService = new AuthService();

  // Verificar autenticação
  const user = await authService.getCurrentUser();
  event.locals.user = user;

  // Verificar autorização para rotas protegidas
  if (event.url.pathname.startsWith('/app')) {
    if (!user) {
      throw redirect(302, '/auth/login');
    }

    // Verificar permissões específicas
    const requiredPermission = getRequiredPermission(event.url.pathname);
    if (requiredPermission && !hasPermission(user, requiredPermission)) {
      throw error(403, 'Acesso negado');
    }

    // Injetar organization_id no contexto
    await setOrganizationContext(user.organization_id);
  }

  return resolve(event);
};

function getRequiredPermission(pathname: string): string | null {
  const permissionMap: Record<string, string> = {
    '/app/financial': 'financial:read',
    '/app/settings': 'settings:read',
    '/app/verification': 'verification:read'
  };

  return permissionMap[pathname] || null;
}

function hasPermission(user: any, permission: string): boolean {
  const rolePermissions = {
    admin: ['*'],
    manager: ['dashboard:read', 'invoices:*', 'catalog:*', 'verification:read', 'dispatch:*', 'issues:*', 'drivers:*', 'financial:*', 'settings:read'],
    operator: ['dashboard:read', 'invoices:create', 'invoices:read', 'catalog:read', 'dispatch:*', 'issues:create'],
    verifier: ['dashboard:read', 'invoices:read', 'verification:*', 'issues:create']
  };

  const userPermissions = rolePermissions[user.role] || [];
  return userPermissions.includes('*') || userPermissions.includes(permission);
}
```

### Validação de Dados

#### Schema Validation com Zod
```typescript
// lib/schemas/invoice.ts
import { z } from 'zod';

export const InvoiceSchema = z.object({
  accessKey: z.string().length(44, 'Chave de acesso deve ter 44 dígitos'),
  invoiceNumber: z.string().min(1, 'Número da NF-e é obrigatório'),
  issuerName: z.string().min(1, 'Nome do emitente é obrigatório'),
  issuerCnpj: z.string().regex(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, 'CNPJ inválido'),
  recipientName: z.string().min(1, 'Nome do destinatário é obrigatório'),
  recipientCnpj: z.string().regex(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, 'CNPJ inválido').optional(),
  totalValue: z.number().positive('Valor deve ser positivo'),
  xmlContent: z.string().min(1, 'Conteúdo XML é obrigatório'),
  status: z.enum(['available', 'in_verification', 'paused', 'verified', 'dispatched', 'completed', 'issue'])
});

export const InvoiceItemSchema = z.object({
  productCode: z.string().min(1, 'Código do produto é obrigatório'),
  description: z.string().min(1, 'Descrição é obrigatória'),
  unit: z.string().min(1, 'Unidade é obrigatória'),
  quantityInvoice: z.number().positive('Quantidade deve ser positiva'),
  unitPrice: z.number().positive('Preço unitário deve ser positivo'),
  totalValue: z.number().positive('Valor total deve ser positivo'),
  ean: z.string().length(13, 'EAN deve ter 13 dígitos').optional()
});
```

#### Sanitização de Inputs
```typescript
// lib/utils/sanitize.ts
import DOMPurify from 'dompurify';

export function sanitizeHtml(html: string): string {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
    ALLOWED_ATTR: []
  });
}

export function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .substring(0, 255);
}

export function sanitizeSearchQuery(query: string): string {
  return query
    .trim()
    .replace(/[<>'"]/g, '')
    .substring(0, 100);
}
```

### Proteção contra Vulnerabilidades

#### CSRF Protection
```typescript
// lib/utils/csrf.ts
import { randomBytes } from 'crypto';

export function generateCSRFToken(): string {
  return randomBytes(32).toString('hex');
}

export function validateCSRFToken(token: string, sessionToken: string): boolean {
  return token === sessionToken;
}
```

#### Rate Limiting
```typescript
// lib/middleware/rateLimit.ts
import { Redis } from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

export async function rateLimit(
  identifier: string,
  limit: number,
  window: number
): Promise<boolean> {
  const key = `rate_limit:${identifier}`;
  const current = await redis.incr(key);

  if (current === 1) {
    await redis.expire(key, window);
  }

  return current <= limit;
}

// Uso em API routes
export async function POST({ request, getClientAddress }) {
  const clientIP = getClientAddress();

  if (!await rateLimit(clientIP, 100, 3600)) { // 100 requests per hour
    throw error(429, 'Rate limit exceeded');
  }

  // Processar request...
}
```

#### SQL Injection Prevention
```typescript
// Sempre usar prepared statements via Supabase
const { data, error } = await supabase
  .from('invoices')
  .select('*')
  .eq('organization_id', organizationId) // Parâmetro seguro
  .ilike('invoice_number', `%${sanitizedQuery}%`); // Query sanitizada
```

### Auditoria e Logs

#### Sistema de Auditoria
```sql
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  user_id UUID REFERENCES users(id) NOT NULL,
  action VARCHAR(50) NOT NULL,
  resource_type VARCHAR(50) NOT NULL,
  resource_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Índices para consultas de auditoria
CREATE INDEX idx_audit_logs_org_date ON audit_logs(organization_id, created_at DESC);
CREATE INDEX idx_audit_logs_user ON audit_logs(user_id, created_at DESC);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
```

```typescript
// lib/services/audit.ts
export class AuditService {
  async logAction(
    userId: string,
    organizationId: string,
    action: string,
    resourceType: string,
    resourceId?: string,
    oldValues?: any,
    newValues?: any,
    metadata?: any
  ) {
    await supabase.from('audit_logs').insert({
      user_id: userId,
      organization_id: organizationId,
      action,
      resource_type: resourceType,
      resource_id: resourceId,
      old_values: oldValues,
      new_values: newValues,
      ip_address: metadata?.ipAddress,
      user_agent: metadata?.userAgent
    });
  }
}
```

---

## 🧪 Testes {#testes}

### Estratégia de Testes

#### Pirâmide de Testes
```
        /\
       /  \
      / E2E \     <- Poucos, críticos
     /______\
    /        \
   / Integration \ <- Moderados, fluxos principais
  /______________\
 /                \
/   Unit Tests     \ <- Muitos, rápidos, isolados
\__________________/
```

### Testes Unitários

#### Configuração Vitest
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import { sveltekit } from '@sveltejs/kit/vite';

export default defineConfig({
  plugins: [sveltekit()],
  test: {
    include: ['src/**/*.{test,spec}.{js,ts}'],
    environment: 'jsdom',
    setupFiles: ['src/test/setup.ts'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*'
      ]
    }
  }
});
```

#### Testes de Serviços
```typescript
// src/lib/services/invoice.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { InvoiceService } from './invoice';
import { createMockSupabaseClient } from '../test/mocks';

describe('InvoiceService', () => {
  let invoiceService: InvoiceService;
  let mockSupabase: any;

  beforeEach(() => {
    mockSupabase = createMockSupabaseClient();
    invoiceService = new InvoiceService(mockSupabase);
  });

  describe('createInvoice', () => {
    it('should create invoice with valid data', async () => {
      const invoiceData = {
        accessKey: '12345678901234567890123456789012345678901234',
        invoiceNumber: '123',
        issuerName: 'Test Company',
        issuerCnpj: '12.345.678/0001-90',
        recipientName: 'Recipient',
        totalValue: 1000.50,
        xmlContent: '<xml>test</xml>'
      };

      mockSupabase.from.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockResolvedValue({
            data: [{ id: 'uuid', ...invoiceData }],
            error: null
          })
        })
      });

      const result = await invoiceService.createInvoice(invoiceData);

      expect(result).toEqual({
        id: 'uuid',
        ...invoiceData
      });
      expect(mockSupabase.from).toHaveBeenCalledWith('invoices');
    });

    it('should throw error for invalid access key', async () => {
      const invalidData = {
        accessKey: '123', // Too short
        invoiceNumber: '123',
        issuerName: 'Test Company',
        totalValue: 1000
      };

      await expect(invoiceService.createInvoice(invalidData))
        .rejects.toThrow('Chave de acesso deve ter 44 dígitos');
    });
  });

  describe('getInvoicesByStatus', () => {
    it('should filter invoices by status', async () => {
      const mockInvoices = [
        { id: '1', status: 'available' },
        { id: '2', status: 'verified' }
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            order: vi.fn().mockResolvedValue({
              data: mockInvoices.filter(i => i.status === 'available'),
              error: null
            })
          })
        })
      });

      const result = await invoiceService.getInvoicesByStatus('available');

      expect(result).toHaveLength(1);
      expect(result[0].status).toBe('available');
    });
  });
});
```

#### Testes de Componentes
```typescript
// src/lib/components/InvoiceCard.test.ts
import { render, screen, fireEvent } from '@testing-library/svelte';
import { describe, it, expect, vi } from 'vitest';
import InvoiceCard from './InvoiceCard.svelte';

describe('InvoiceCard', () => {
  const mockInvoice = {
    id: '1',
    invoiceNumber: '123',
    issuerName: 'Test Company',
    totalValue: 1000.50,
    status: 'available',
    createdAt: '2024-01-01T00:00:00Z'
  };

  it('should render invoice information', () => {
    render(InvoiceCard, { props: { invoice: mockInvoice } });

    expect(screen.getByText('NF-e 123')).toBeInTheDocument();
    expect(screen.getByText('Test Company')).toBeInTheDocument();
    expect(screen.getByText('R$ 1.000,50')).toBeInTheDocument();
  });

  it('should show correct status badge', () => {
    render(InvoiceCard, { props: { invoice: mockInvoice } });

    const statusBadge = screen.getByText('Disponível');
    expect(statusBadge).toBeInTheDocument();
    expect(statusBadge).toHaveClass('bg-emerald-100');
  });

  it('should emit click event when clicked', async () => {
    const { component } = render(InvoiceCard, { props: { invoice: mockInvoice } });
    const clickHandler = vi.fn();

    component.$on('click', clickHandler);

    const card = screen.getByRole('button');
    await fireEvent.click(card);

    expect(clickHandler).toHaveBeenCalledWith(
      expect.objectContaining({
        detail: mockInvoice
      })
    );
  });

  it('should show loading state', () => {
    render(InvoiceCard, {
      props: {
        invoice: mockInvoice,
        loading: true
      }
    });

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });
});
```

### Testes de Integração

#### Testes de API Routes
```typescript
// src/routes/api/invoices/+server.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { POST } from './+server';
import { createMockRequestEvent } from '../../../test/helpers';

describe('/api/invoices', () => {
  beforeEach(async () => {
    // Setup test database
    await setupTestDatabase();
  });

  afterEach(async () => {
    // Cleanup test database
    await cleanupTestDatabase();
  });

  describe('POST', () => {
    it('should create invoice successfully', async () => {
      const requestEvent = createMockRequestEvent({
        method: 'POST',
        body: {
          accessKey: '12345678901234567890123456789012345678901234',
          invoiceNumber: '123',
          issuerName: 'Test Company',
          totalValue: 1000.50,
          xmlContent: '<xml>test</xml>'
        },
        user: { id: 'user-1', organizationId: 'org-1', role: 'operator' }
      });

      const response = await POST(requestEvent);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.invoice).toMatchObject({
        accessKey: '12345678901234567890123456789012345678901234',
        invoiceNumber: '123',
        status: 'available'
      });
    });

    it('should return 400 for invalid data', async () => {
      const requestEvent = createMockRequestEvent({
        method: 'POST',
        body: {
          accessKey: '123', // Invalid
          invoiceNumber: '123'
        },
        user: { id: 'user-1', organizationId: 'org-1', role: 'operator' }
      });

      const response = await POST(requestEvent);

      expect(response.status).toBe(400);
    });

    it('should return 403 for unauthorized user', async () => {
      const requestEvent = createMockRequestEvent({
        method: 'POST',
        body: { /* valid data */ },
        user: { id: 'user-1', organizationId: 'org-1', role: 'verifier' } // No permission
      });

      const response = await POST(requestEvent);

      expect(response.status).toBe(403);
    });
  });
});
```

### Testes End-to-End

#### Configuração Playwright
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:4173',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] }
    }
  ],
  webServer: {
    command: 'npm run build && npm run preview',
    port: 4173
  }
});
```

#### Testes de Fluxo Completo
```typescript
// tests/e2e/invoice-flow.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Invoice Management Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/auth/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');

    // Wait for dashboard
    await expect(page).toHaveURL('/dashboard');
  });

  test('should complete full invoice verification flow', async ({ page }) => {
    // 1. Upload invoice
    await page.goto('/invoices');
    await page.click('[data-testid="upload-button"]');

    // Upload XML file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles('tests/fixtures/sample-invoice.xml');
    await page.click('[data-testid="process-button"]');

    // Wait for processing
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await page.click('[data-testid="close-modal"]');

    // 2. Start verification
    await page.goto('/verification');
    await page.click('[data-testid="select-invoice"]:first-child');
    await page.selectOption('[data-testid="verification-type"]', 'volume');
    await page.click('[data-testid="start-verification"]');

    // 3. Perform verification
    await expect(page.locator('[data-testid="verification-interface"]')).toBeVisible();

    // Switch to scanner mode
    await page.click('[data-testid="scanner-mode"]');

    // Scan items (simulate)
    const items = await page.locator('[data-testid="verification-item"]').all();
    for (const item of items) {
      const ean = await item.getAttribute('data-ean');
      await page.fill('[data-testid="scan-input"]', ean);
      await page.press('[data-testid="scan-input"]', 'Enter');
    }

    // Complete verification
    await page.click('[data-testid="complete-verification"]');
    await expect(page.locator('[data-testid="verification-success"]')).toBeVisible();

    // 4. Dispatch invoice
    await page.goto('/dispatch');
    await page.click('[data-testid="select-invoice"]:first-child');
    await page.selectOption('[data-testid="driver-select"]', 'driver-1');
    await page.click('[data-testid="create-route"]');

    await expect(page.locator('[data-testid="route-created"]')).toBeVisible();

    // 5. Verify final status
    await page.goto('/invoices');
    await page.click('[data-testid="tab-dispatched"]');

    const dispatchedInvoice = page.locator('[data-testid="invoice-card"]').first();
    await expect(dispatchedInvoice.locator('[data-testid="status-badge"]')).toHaveText('Despachada');
  });

  test('should handle verification with divergences', async ({ page }) => {
    // Setup invoice with known divergences
    await setupTestInvoiceWithDivergences();

    await page.goto('/verification');
    await page.click('[data-testid="select-invoice"]:first-child');
    await page.selectOption('[data-testid="verification-type"]', 'simple');
    await page.click('[data-testid="start-verification"]');

    // Create divergence
    const firstItem = page.locator('[data-testid="verification-item"]').first();
    await firstItem.locator('[data-testid="quantity-input"]').fill('5'); // Different from invoice

    // Should show divergence alert
    await expect(firstItem.locator('[data-testid="divergence-alert"]')).toBeVisible();

    // Complete with divergence
    await page.click('[data-testid="complete-verification"]');

    // Should create automatic issue
    await page.goto('/issues');
    await expect(page.locator('[data-testid="issue-card"]').first()).toContainText('Divergência na conferência');
  });
});
```

### Testes de Performance

#### Lighthouse CI
```javascript
// lighthouserc.js
module.exports = {
  ci: {
    collect: {
      url: [
        'http://localhost:4173/dashboard',
        'http://localhost:4173/invoices',
        'http://localhost:4173/catalog',
        'http://localhost:4173/verification'
      ],
      numberOfRuns: 3
    },
    assert: {
      assertions: {
        'categories:performance': ['warn', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['warn', { minScore: 0.8 }]
      }
    },
    upload: {
      target: 'temporary-public-storage'
    }
  }
};
```

#### Load Testing
```typescript
// tests/load/invoice-api.test.ts
import { check } from 'k6';
import http from 'k6/http';

export const options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 20 }, // Ramp up to 20 users
    { duration: '5m', target: 20 }, // Stay at 20 users
    { duration: '2m', target: 0 },  // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    http_req_failed: ['rate<0.1'],    // Error rate under 10%
  }
};

export default function () {
  const payload = JSON.stringify({
    accessKey: '12345678901234567890123456789012345678901234',
    invoiceNumber: `${Math.random()}`,
    issuerName: 'Load Test Company',
    totalValue: 1000.50,
    xmlContent: '<xml>test</xml>'
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-token'
    }
  };

  const response = http.post('http://localhost:5173/api/invoices', payload, params);

  check(response, {
    'status is 201': (r) => r.status === 201,
    'response time < 500ms': (r) => r.timings.duration < 500,
    'has invoice id': (r) => JSON.parse(r.body).invoice.id !== undefined
  });
}
```

---

## 🚀 Deploy e DevOps {#deploy}

### Configuração do Deploy

#### Vercel Configuration
```json
// vercel.json
{
  "framework": "sveltekit",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "src/routes/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "env": {
    "PUBLIC_SUPABASE_URL": "@supabase-url",
    "PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key",
    "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-key"
  },
  "build": {
    "env": {
      "NODE_ENV": "production"
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "https://yourdomain.com"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type, Authorization"
        }
      ]
    }
  ]
}
```

#### Environment Variables
```bash
# .env.example
# Supabase
PUBLIC_SUPABASE_URL=https://your-project.supabase.co
PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# App Configuration
PUBLIC_APP_NAME=OrderFlow
PUBLIC_APP_URL=https://yourapp.vercel.app

# External APIs
NF_API_URL=https://api.nf-provider.com
NF_API_KEY=your-nf-api-key

# Monitoring
SENTRY_DSN=https://your-sentry-dsn
VERCEL_ANALYTICS_ID=your-analytics-id

# Email (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### CI/CD Pipeline

#### GitHub Actions
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run type checking
        run: npm run check

      - name: Run unit tests
        run: npm run test:unit
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

      - name: Build application
        run: npm run build

      - name: Run E2E tests
        run: npm run test:e2e
        env:
          CI: true

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  lighthouse:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Run Lighthouse CI
        run: npm run lighthouse:ci
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  deploy:
    runs-on: ubuntu-latest
    needs: [test, lighthouse]
    if: github.ref == 'refs/heads/main'

    steps:
      - uses: actions/checkout@v4

      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
```

### Database Migrations

#### Supabase Migrations
```sql
-- migrations/001_initial_schema.sql
-- Create organizations table
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  cnpj VARCHAR(14) UNIQUE,
  plan VARCHAR(50) DEFAULT 'basic',
  status VARCHAR(20) DEFAULT 'active',
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Organizations are viewable by members" ON organizations
  FOR SELECT USING (
    id IN (
      SELECT organization_id FROM users WHERE id = auth.uid()
    )
  );

-- Create indexes
CREATE INDEX idx_organizations_cnpj ON organizations(cnpj);
CREATE INDEX idx_organizations_status ON organizations(status);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_organizations_updated_at
  BEFORE UPDATE ON organizations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### Migration Runner
```typescript
// scripts/migrate.ts
import { createClient } from '@supabase/supabase-js';
import { readFileSync, readdirSync } from 'fs';
import { join } from 'path';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function runMigrations() {
  const migrationsDir = join(process.cwd(), 'migrations');
  const migrationFiles = readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort();

  for (const file of migrationFiles) {
    console.log(`Running migration: ${file}`);

    const sql = readFileSync(join(migrationsDir, file), 'utf-8');
    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error(`Migration ${file} failed:`, error);
      process.exit(1);
    }

    console.log(`Migration ${file} completed successfully`);
  }

  console.log('All migrations completed');
}

runMigrations().catch(console.error);
```

---

## ⚡ Performance e Escalabilidade {#performance}

### Otimizações de Frontend

#### Code Splitting e Lazy Loading
```typescript
// src/routes/+layout.ts
import type { LayoutLoad } from './$types';

export const load: LayoutLoad = async ({ url, depends }) => {
  depends('app:auth');

  // Lazy load components based on route
  const routeComponents = {
    '/dashboard': () => import('../lib/components/dashboard/DashboardStats.svelte'),
    '/invoices': () => import('../lib/components/invoices/InvoiceUpload.svelte'),
    '/catalog': () => import('../lib/components/catalog/ProductCard.svelte'),
    '/verification': () => import('../lib/components/verification/VerificationInterface.svelte')
  };

  const currentRoute = url.pathname;
  const componentLoader = routeComponents[currentRoute];

  return {
    lazyComponent: componentLoader ? componentLoader() : null
  };
};
```

#### Virtual Scrolling para Listas Grandes
```svelte
<!-- VirtualList.svelte -->
<script lang="ts">
  import { onMount, tick } from 'svelte';

  export let items: any[] = [];
  export let itemHeight = 80;
  export let containerHeight = 400;
  export let overscan = 5;

  let scrollTop = 0;
  let containerElement: HTMLElement;

  $: visibleStart = Math.floor(scrollTop / itemHeight);
  $: visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight) + overscan,
    items.length
  );
  $: visibleItems = items.slice(
    Math.max(0, visibleStart - overscan),
    visibleEnd
  );
  $: offsetY = Math.max(0, visibleStart - overscan) * itemHeight;

  function handleScroll(event: Event) {
    scrollTop = (event.target as HTMLElement).scrollTop;
  }
</script>

<div
  class="virtual-list-container"
  style="height: {containerHeight}px; overflow-y: auto;"
  on:scroll={handleScroll}
  bind:this={containerElement}
>
  <div
    class="virtual-list-spacer"
    style="height: {items.length * itemHeight}px; position: relative;"
  >
    <div
      class="virtual-list-items"
      style="transform: translateY({offsetY}px);"
    >
      {#each visibleItems as item, index (item.id)}
        <div
          class="virtual-list-item"
          style="height: {itemHeight}px;"
        >
          <slot {item} index={visibleStart - overscan + index} />
        </div>
      {/each}
    </div>
  </div>
</div>
```

#### Otimização de Imagens
```typescript
// lib/utils/imageOptimization.ts
export function generateImageSrcSet(
  baseUrl: string,
  sizes: number[] = [320, 640, 1024, 1280]
): string {
  return sizes
    .map(size => `${baseUrl}?w=${size}&q=80 ${size}w`)
    .join(', ');
}

export function getOptimalImageSize(containerWidth: number): number {
  const sizes = [320, 640, 1024, 1280, 1920];
  return sizes.find(size => size >= containerWidth * 1.2) || sizes[sizes.length - 1];
}
```

### Otimizações de Backend

#### Caching Strategy
```typescript
// lib/services/cache.ts
import { Redis } from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

export class CacheService {
  private defaultTTL = 3600; // 1 hour

  async get<T>(key: string): Promise<T | null> {
    const cached = await redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }

  async set(key: string, value: any, ttl = this.defaultTTL): Promise<void> {
    await redis.setex(key, ttl, JSON.stringify(value));
  }

  async invalidate(pattern: string): Promise<void> {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
  }

  // Cache específico para organizações
  async getCachedOrganizationData(orgId: string): Promise<any> {
    return this.get(`org:${orgId}:data`);
  }

  async setCachedOrganizationData(orgId: string, data: any): Promise<void> {
    await this.set(`org:${orgId}:data`, data, 1800); // 30 minutes
  }

  // Cache para estatísticas do dashboard
  async getCachedDashboardStats(orgId: string, userId: string): Promise<any> {
    return this.get(`dashboard:${orgId}:${userId}`);
  }

  async setCachedDashboardStats(orgId: string, userId: string, stats: any): Promise<void> {
    await this.set(`dashboard:${orgId}:${userId}`, stats, 300); // 5 minutes
  }
}
```

#### Database Query Optimization
```sql
-- Índices compostos para queries frequentes
CREATE INDEX CONCURRENTLY idx_invoices_org_status_created
ON invoices(organization_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY idx_products_org_active_category
ON products(organization_id, active, category)
WHERE active = true;

CREATE INDEX CONCURRENTLY idx_verifications_org_status_date
ON verifications(organization_id, status, started_at DESC);

-- Índices parciais para melhor performance
CREATE INDEX CONCURRENTLY idx_invoices_active_statuses
ON invoices(organization_id, created_at DESC)
WHERE status IN ('available', 'in_verification', 'verified');

-- Índices para full-text search
CREATE INDEX CONCURRENTLY idx_products_search
ON products USING gin(to_tsvector('portuguese', main_name));

-- Materialized view para estatísticas pesadas
CREATE MATERIALIZED VIEW organization_stats AS
SELECT
  o.id as organization_id,
  o.name,
  COUNT(DISTINCT i.id) as total_invoices,
  COUNT(DISTINCT p.id) as total_products,
  COUNT(DISTINCT u.id) as total_users,
  COUNT(DISTINCT d.id) as total_drivers,
  AVG(i.total_value) as avg_invoice_value,
  MAX(i.created_at) as last_invoice_date
FROM organizations o
LEFT JOIN invoices i ON i.organization_id = o.id
LEFT JOIN products p ON p.organization_id = o.id
LEFT JOIN users u ON u.organization_id = o.id
LEFT JOIN drivers d ON d.organization_id = o.id
GROUP BY o.id, o.name;

-- Refresh automático da materialized view
CREATE OR REPLACE FUNCTION refresh_organization_stats()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY organization_stats;
END;
$$ LANGUAGE plpgsql;

-- Agendar refresh a cada hora
SELECT cron.schedule('refresh-org-stats', '0 * * * *', 'SELECT refresh_organization_stats();');
```

#### Connection Pooling
```typescript
// lib/database/pool.ts
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20, // Maximum number of connections
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
  maxUses: 7500, // Close connection after 7500 uses
});

export async function query(text: string, params?: any[]) {
  const start = Date.now();
  const client = await pool.connect();

  try {
    const result = await client.query(text, params);
    const duration = Date.now() - start;

    // Log slow queries
    if (duration > 1000) {
      console.warn(`Slow query detected: ${duration}ms`, { text, params });
    }

    return result;
  } finally {
    client.release();
  }
}
```

### Monitoramento de Performance

#### Web Vitals Tracking
```typescript
// lib/analytics/webVitals.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric: any) {
  // Send to your analytics service
  fetch('/api/analytics/web-vitals', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(metric)
  });
}

export function trackWebVitals() {
  getCLS(sendToAnalytics);
  getFID(sendToAnalytics);
  getFCP(sendToAnalytics);
  getLCP(sendToAnalytics);
  getTTFB(sendToAnalytics);
}
```

---

## 📊 Monitoramento {#monitoramento}

### Error Tracking com Sentry

#### Configuração
```typescript
// src/hooks.client.ts
import * as Sentry from '@sentry/sveltekit';

Sentry.init({
  dsn: 'YOUR_SENTRY_DSN',
  integrations: [
    new Sentry.BrowserTracing(),
    new Sentry.Replay()
  ],
  tracesSampleRate: 1.0,
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,
  environment: process.env.NODE_ENV,
  beforeSend(event) {
    // Filter out sensitive data
    if (event.request?.data) {
      delete event.request.data.password;
      delete event.request.data.token;
    }
    return event;
  }
});
```

#### Error Boundaries
```svelte
<!-- ErrorBoundary.svelte -->
<script lang="ts">
  import { onMount } from 'svelte';
  import * as Sentry from '@sentry/sveltekit';

  export let fallback: any = null;

  let error: Error | null = null;
  let errorId: string | null = null;

  function handleError(event: ErrorEvent) {
    error = event.error;
    errorId = Sentry.captureException(error);
  }

  function retry() {
    error = null;
    errorId = null;
  }

  onMount(() => {
    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  });
</script>

{#if error}
  <div class="error-boundary">
    <div class="error-content">
      <h2>Algo deu errado</h2>
      <p>Ocorreu um erro inesperado. Nossa equipe foi notificada.</p>
      {#if errorId}
        <p class="error-id">ID do erro: {errorId}</p>
      {/if}
      <div class="error-actions">
        <button on:click={retry} class="btn-primary">
          Tentar novamente
        </button>
        <button on:click={() => window.location.reload()} class="btn-secondary">
          Recarregar página
        </button>
      </div>
    </div>
  </div>
{:else}
  <slot />
{/if}
```

### Analytics e Métricas

#### Custom Analytics
```typescript
// lib/analytics/tracker.ts
export class AnalyticsTracker {
  private queue: any[] = [];
  private isOnline = navigator.onLine;

  constructor() {
    // Flush queue when online
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  track(event: string, properties: Record<string, any> = {}) {
    const payload = {
      event,
      properties: {
        ...properties,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent
      }
    };

    if (this.isOnline) {
      this.send(payload);
    } else {
      this.queue.push(payload);
    }
  }

  private async send(payload: any) {
    try {
      await fetch('/api/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
    } catch (error) {
      console.warn('Analytics tracking failed:', error);
      this.queue.push(payload);
    }
  }

  private async flushQueue() {
    while (this.queue.length > 0) {
      const payload = this.queue.shift();
      await this.send(payload);
    }
  }

  // Business-specific tracking methods
  trackInvoiceUpload(count: number, method: 'xml' | 'access_key') {
    this.track('invoice_upload', { count, method });
  }

  trackVerificationCompleted(type: string, duration: number, divergences: number) {
    this.track('verification_completed', { type, duration, divergences });
  }

  trackRouteCreated(invoiceCount: number, driverId: string) {
    this.track('route_created', { invoiceCount, driverId });
  }
}

export const analytics = new AnalyticsTracker();
```

### Health Checks

#### System Health Monitoring
```typescript
// src/routes/api/health/+server.ts
import type { RequestHandler } from './$types';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export const GET: RequestHandler = async () => {
  const checks = {
    database: false,
    storage: false,
    auth: false,
    timestamp: new Date().toISOString()
  };

  try {
    // Database check
    const { error: dbError } = await supabase
      .from('organizations')
      .select('id')
      .limit(1);
    checks.database = !dbError;

    // Storage check
    const { error: storageError } = await supabase.storage
      .from('invoices')
      .list('', { limit: 1 });
    checks.storage = !storageError;

    // Auth check
    const { error: authError } = await supabase.auth.admin.listUsers({
      page: 1,
      perPage: 1
    });
    checks.auth = !authError;

    const allHealthy = Object.values(checks).every(check =>
      typeof check === 'boolean' ? check : true
    );

    return new Response(JSON.stringify(checks), {
      status: allHealthy ? 200 : 503,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      ...checks,
      error: error.message
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
```

### Logging Estruturado

#### Logger Service
```typescript
// lib/services/logger.ts
import pino from 'pino';

const logger = pino({
  level: process.env.LOG_LEVEL || 'info',
  formatters: {
    level: (label) => ({ level: label }),
    bindings: (bindings) => ({
      pid: bindings.pid,
      hostname: bindings.hostname,
      environment: process.env.NODE_ENV
    })
  },
  serializers: {
    req: pino.stdSerializers.req,
    res: pino.stdSerializers.res,
    err: pino.stdSerializers.err
  }
});

export class Logger {
  static info(message: string, meta?: any) {
    logger.info(meta, message);
  }

  static warn(message: string, meta?: any) {
    logger.warn(meta, message);
  }

  static error(message: string, error?: Error, meta?: any) {
    logger.error({ err: error, ...meta }, message);
  }

  static debug(message: string, meta?: any) {
    logger.debug(meta, message);
  }

  // Business-specific logging
  static logInvoiceProcessed(invoiceId: string, organizationId: string, userId: string) {
    this.info('Invoice processed', {
      invoiceId,
      organizationId,
      userId,
      action: 'invoice_processed'
    });
  }

  static logVerificationCompleted(verificationId: string, duration: number, divergences: number) {
    this.info('Verification completed', {
      verificationId,
      duration,
      divergences,
      action: 'verification_completed'
    });
  }

  static logSecurityEvent(event: string, userId: string, ipAddress: string, details?: any) {
    this.warn('Security event', {
      event,
      userId,
      ipAddress,
      details,
      action: 'security_event'
    });
  }
}
```

---

## 📚 Considerações Finais

### Roadmap de Implementação

#### Fase 1 - MVP (4-6 semanas)
- [ ] Setup inicial do projeto (SvelteKit + Supabase)
- [ ] Autenticação e sistema multi-tenant
- [ ] CRUD básico de organizações e usuários
- [ ] Upload e processamento de NF-e
- [ ] Interface básica de listagem
- [ ] Deploy inicial na Vercel

#### Fase 2 - Core Features (6-8 semanas)
- [ ] Sistema de conferência completo
- [ ] Catálogo inteligente com alertas
- [ ] Dashboard com métricas básicas
- [ ] Sistema de expedição
- [ ] Gestão de motoristas
- [ ] Testes unitários e E2E

#### Fase 3 - Advanced Features (4-6 semanas)
- [ ] Sistema financeiro completo
- [ ] Relatórios e exportações
- [ ] Sistema de ocorrências
- [ ] Notificações em tempo real
- [ ] Otimizações de performance
- [ ] Monitoramento completo

#### Fase 4 - Polish & Scale (2-4 semanas)
- [ ] PWA capabilities
- [ ] Offline support
- [ ] Advanced analytics
- [ ] API pública
- [ ] Documentação completa
- [ ] Onboarding automatizado

### Métricas de Sucesso

#### Técnicas
- **Performance**: LCP < 2.5s, FID < 100ms, CLS < 0.1
- **Disponibilidade**: 99.9% uptime
- **Segurança**: Zero vulnerabilidades críticas
- **Cobertura de Testes**: > 80%

#### Negócio
- **Eficiência**: Redução de 60% no tempo de conferência
- **Precisão**: < 2% de divergências não detectadas
- **Satisfação**: NPS > 70
- **Adoção**: 90% dos usuários ativos semanalmente

### Considerações de Escalabilidade

#### Limites por Plano
```typescript
const PLAN_LIMITS = {
  basic: {
    maxUsers: 5,
    maxInvoicesPerMonth: 1000,
    maxStorageGB: 1,
    maxAPICallsPerDay: 10000
  },
  professional: {
    maxUsers: 20,
    maxInvoicesPerMonth: 5000,
    maxStorageGB: 10,
    maxAPICallsPerDay: 50000
  },
  enterprise: {
    maxUsers: -1, // unlimited
    maxInvoicesPerMonth: -1,
    maxStorageGB: 100,
    maxAPICallsPerDay: 200000
  }
};
```

#### Estratégias de Crescimento
- **Horizontal Scaling**: Supabase auto-scaling
- **Caching**: Redis para dados frequentes
- **CDN**: Vercel Edge Network
- **Database**: Read replicas para relatórios
- **Background Jobs**: Queue system para processamento pesado

### Manutenção e Suporte

#### Versionamento
- **Semantic Versioning**: MAJOR.MINOR.PATCH
- **Feature Flags**: Rollout gradual de features
- **Backward Compatibility**: Mínimo 2 versões
- **Migration Strategy**: Zero-downtime deployments

#### Documentação
- **API Documentation**: OpenAPI/Swagger
- **User Guide**: Gitbook ou similar
- **Developer Docs**: Storybook para componentes
- **Changelog**: Automated release notes

---

## 🎯 Conclusão

Esta documentação representa um guia completo para implementação de um sistema SaaS moderno e escalável para gestão de NF-e. A arquitetura proposta combina as melhores práticas de desenvolvimento com tecnologias robustas e comprovadas.

### Pontos Fortes da Arquitetura:
1. **Escalabilidade**: Multi-tenant com isolamento completo
2. **Performance**: Otimizações em todas as camadas
3. **Segurança**: RLS, autenticação robusta, auditoria completa
4. **Manutenibilidade**: Código limpo, testes abrangentes, CI/CD
5. **Experiência do Usuário**: Interface moderna e responsiva

### Próximos Passos:
1. **Setup do Ambiente**: Configurar repositório e ferramentas
2. **Prototipagem**: Implementar MVP das funcionalidades core
3. **Validação**: Testes com usuários reais
4. **Iteração**: Refinamento baseado em feedback
5. **Lançamento**: Deploy em produção com monitoramento

Esta documentação deve ser tratada como um documento vivo, atualizado conforme o projeto evolui e novos requisitos surgem.

---

*Documentação técnica completa - v1.0*
*Criada para desenvolvimento de sistema SaaS de gestão de NF-e*
*Stack: SvelteKit + TailwindCSS + Supabase + Vercel*
