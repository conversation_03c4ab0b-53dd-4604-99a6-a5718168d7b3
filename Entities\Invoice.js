{"name": "Invoice", "type": "object", "properties": {"access_key": {"type": "string", "description": "NF-e access key (44 digits)"}, "invoice_number": {"type": "string", "description": "Invoice number"}, "issuer_name": {"type": "string", "description": "Issuer company name"}, "issuer_cnpj": {"type": "string", "description": "Issuer CNPJ"}, "recipient_name": {"type": "string", "description": "Recipient company name"}, "recipient_cnpj": {"type": "string", "description": "Recipient CNPJ"}, "total_value": {"type": "number", "description": "Total invoice value"}, "xml_content": {"type": "string", "description": "XML file content"}, "status": {"type": "string", "enum": ["available", "in_verification", "paused", "verified", "dispatched", "completed", "issue"], "default": "available", "description": "Invoice processing status"}, "volume_code": {"type": "string", "description": "Volume identifier (A, B, AA, etc.)"}, "verification_type": {"type": "string", "enum": ["simple", "volume", "detailed"], "description": "Type of verification applied"}}, "required": ["access_key", "invoice_number", "issuer_name", "total_value"]}