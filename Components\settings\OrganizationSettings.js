import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Organization } from "@/entities/all";
import { 
  Building2, 
  Save,
  Settings as SettingsIcon,
  CreditCard,
  Globe
} from "lucide-react";

export default function OrganizationSettings({ organization, currentUser, onUpdate }) {
  const [formData, setFormData] = useState({
    name: organization?.name || '',
    cnpj: organization?.cnpj || '',
    plan: organization?.plan || 'basic',
    settings: organization?.settings || {}
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (organization) {
        await Organization.update(organization.id, formData);
      } else {
        await Organization.create(formData);
      }
      onUpdate();
      alert("Configurações salvas com sucesso!");
    } catch (error) {
      console.error("Error saving organization:", error);
      alert("Erro ao salvar configurações. Tente novamente.");
    }
    setIsSubmitting(false);
  };

  const getPlanLabel = (plan) => {
    const labels = {
      basic: "Básico",
      premium: "Premium", 
      enterprise: "Enterprise"
    };
    return labels[plan] || plan;
  };

  const getPlanBadgeColor = (plan) => {
    const colors = {
      basic: "bg-slate-100 text-slate-700 border-slate-200",
      premium: "bg-blue-100 text-blue-700 border-blue-200",
      enterprise: "bg-purple-100 text-purple-700 border-purple-200"
    };
    return colors[plan] || "bg-slate-100 text-slate-700 border-slate-200";
  };

  return (
    <div className="space-y-6">
      {/* Organization Info */}
      <Card className="border-none shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-emerald-600" />
            Informações da Organização
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Nome da Empresa</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="cnpj">CNPJ</Label>
                <Input
                  id="cnpj"
                  value={formData.cnpj}
                  onChange={(e) => setFormData({...formData, cnpj: e.target.value})}
                  placeholder="00.000.000/0000-00"
                  className="mt-1"
                />
              </div>
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={isSubmitting || currentUser?.role !== 'admin'}>
                <Save className="w-4 h-4 mr-2" />
                {isSubmitting ? 'Salvando...' : 'Salvar Alterações'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Plan Information */}
      <Card className="border-none shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5 text-blue-600" />
            Plano Atual
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div>
              <Badge className={getPlanBadgeColor(formData.plan)}>
                {getPlanLabel(formData.plan)}
              </Badge>
              <p className="text-sm text-slate-600 mt-2">
                {formData.plan === 'basic' && 'Até 100 NF-e por mês, 5 usuários'}
                {formData.plan === 'premium' && 'Até 1.000 NF-e por mês, 15 usuários'}
                {formData.plan === 'enterprise' && 'NF-e ilimitadas, usuários ilimitados'}
              </p>
            </div>
            {currentUser?.role === 'admin' && (
              <Button variant="outline">
                Gerenciar Plano
              </Button>
            )}
          </div>

          {/* Plan Features */}
          <div className="grid md:grid-cols-3 gap-4">
            <div className="p-3 bg-slate-50 rounded-lg">
              <p className="text-sm font-medium text-slate-900">NF-e Processadas</p>
              <p className="text-lg font-bold text-slate-700">
                {organization?.settings?.invoices_count || 0}
                {formData.plan === 'basic' && ' / 100'}
                {formData.plan === 'premium' && ' / 1.000'}
              </p>
            </div>
            <div className="p-3 bg-slate-50 rounded-lg">
              <p className="text-sm font-medium text-slate-900">Usuários Ativos</p>
              <p className="text-lg font-bold text-slate-700">
                {organization?.settings?.users_count || 0}
                {formData.plan === 'basic' && ' / 5'}
                {formData.plan === 'premium' && ' / 15'}
              </p>
            </div>
            <div className="p-3 bg-slate-50 rounded-lg">
              <p className="text-sm font-medium text-slate-900">Produtos Catalogados</p>
              <p className="text-lg font-bold text-slate-700">
                {organization?.settings?.products_count || 0}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Settings */}
      <Card className="border-none shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SettingsIcon className="w-5 h-5 text-slate-600" />
            Configurações do Sistema
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label>Geração Automática de Volumes</Label>
                <Select 
                  value={formData.settings?.auto_volume_generation || 'enabled'}
                  onValueChange={(value) => setFormData({
                    ...formData, 
                    settings: {...formData.settings, auto_volume_generation: value}
                  })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="enabled">Habilitado</SelectItem>
                    <SelectItem value="disabled">Desabilitado</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Tipo de Conferência Padrão</Label>
                <Select 
                  value={formData.settings?.default_verification_type || 'simple'}
                  onValueChange={(value) => setFormData({
                    ...formData, 
                    settings: {...formData.settings, default_verification_type: value}
                  })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="simple">Simples</SelectItem>
                    <SelectItem value="volume">Por Volume</SelectItem>
                    <SelectItem value="detailed">Detalhada</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label>Observações Internas</Label>
              <Textarea
                value={formData.settings?.internal_notes || ''}
                onChange={(e) => setFormData({
                  ...formData,
                  settings: {...formData.settings, internal_notes: e.target.value}
                })}
                placeholder="Notas internas sobre configurações específicas..."
                className="mt-1"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
