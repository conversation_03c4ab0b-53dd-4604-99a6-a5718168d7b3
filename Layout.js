
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { User } from "@/entities/User";
import {
  LayoutDashboard,
  FileText,
  Package,
  CheckCircle,
  Truck,
  AlertTriangle,
  Users,
  Settings,
  Bell,
  Menu,
  LogOut,
  DollarSign // Added DollarSign import
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

const navigationItems = [
  {
    title: "Dashboard",
    url: createPageUrl("Dashboard"),
    icon: LayoutDashboard,
    roles: ["admin", "manager", "operator", "verifier"]
  },
  {
    title: "<PERSON><PERSON>",
    url: createPageUrl("Invoices"),
    icon: FileText,
    roles: ["admin", "manager", "operator", "verifier"]
  },
  {
    title: "Catálogo",
    url: createPageUrl("Catalog"),
    icon: Package,
    roles: ["admin", "manager", "operator"]
  },
  {
    title: "Conferência",
    url: createPageUrl("Verification"),
    icon: CheckCircle,
    roles: ["admin", "manager", "verifier"]
  },
  {
    title: "Expedição",
    url: createPageUrl("Dispatch"),
    icon: Truck,
    roles: ["admin", "manager", "operator"]
  },
  {
    title: "Financeiro", // Added new item
    url: createPageUrl("Financial"), // Added new item
    icon: DollarSign, // Added new item
    roles: ["admin", "manager"] // Added new item
  },
  {
    title: "Ocorrências",
    url: createPageUrl("Issues"),
    icon: AlertTriangle,
    roles: ["admin", "manager", "operator", "verifier"]
  },
  {
    title: "Motoristas",
    url: createPageUrl("Drivers"),
    icon: Users,
    roles: ["admin", "manager"]
  }
];

export default function Layout({ children, currentPageName }) {
  const location = useLocation();
  const [currentUser, setCurrentUser] = React.useState(null);
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    loadCurrentUser();
  }, []);

  const loadCurrentUser = async () => {
    try {
      const user = await User.me();
      setCurrentUser(user);
    } catch (error) {
      console.error("Error loading user:", error);
    }
    setIsLoading(false);
  };

  const handleLogout = async () => {
    try {
      await User.logout();
      // Optionally redirect to login or clear user state
      setCurrentUser(null);
      // Example: window.location.href = '/login';
    } catch (error) {
      console.error("Error logging out:", error);
    }
  };

  const filteredNavItems = navigationItems.filter(item => 
    !currentUser || item.roles.includes(currentUser.role)
  );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gradient-to-br from-slate-50 to-slate-100">
        <Sidebar className="border-r border-slate-200 bg-white/80 backdrop-blur-sm">
          <SidebarHeader className="border-b border-slate-200 p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-emerald-600 rounded-xl flex items-center justify-center shadow-md">
                <FileText className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="font-bold text-slate-900 text-lg">OrderFlow</h2>
                <p className="text-xs text-slate-500 font-medium">Sistema de Gestão</p>
              </div>
            </div>
          </SidebarHeader>
          
          <SidebarContent className="p-3">
            <SidebarGroup>
              <SidebarGroupLabel className="text-xs font-semibold text-slate-500 uppercase tracking-wide px-3 py-3">
                Navegação
              </SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {filteredNavItems.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton 
                        asChild 
                        className={`hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 rounded-xl mb-1 group ${
                          location.pathname === item.url ? 'bg-gradient-to-r from-blue-50 to-emerald-50 text-blue-700 border border-blue-100' : ''
                        }`}
                      >
                        <Link to={item.url} className="flex items-center gap-3 px-4 py-3">
                          <item.icon className={`w-4 h-4 transition-colors ${
                            location.pathname === item.url ? 'text-blue-600' : 'text-slate-500 group-hover:text-blue-600'
                          }`} />
                          <span className="font-medium text-sm">{item.title}</span>
                          {item.title === "Ocorrências" && (
                            <Badge variant="destructive" className="ml-auto text-xs">3</Badge>
                          )}
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            {(currentUser?.role === 'admin' || currentUser?.role === 'manager') && (
              <SidebarGroup>
                <SidebarGroupLabel className="text-xs font-semibold text-slate-500 uppercase tracking-wide px-3 py-3">
                  Administração
                </SidebarGroupLabel>
                <SidebarGroupContent>
                  <SidebarMenu>
                    <SidebarMenuItem>
                      <SidebarMenuButton 
                        asChild 
                        className="hover:bg-slate-50 hover:text-slate-700 transition-all duration-200 rounded-xl mb-1"
                      >
                        <Link to={createPageUrl("Settings")} className="flex items-center gap-3 px-4 py-3">
                          <Settings className="w-4 h-4 text-slate-500" />
                          <span className="font-medium text-sm">Configurações</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>
            )}

            <SidebarGroup>
              <SidebarGroupLabel className="text-xs font-semibold text-slate-500 uppercase tracking-wide px-3 py-3">
                Status Hoje
              </SidebarGroupLabel>
              <SidebarGroupContent>
                <div className="px-4 py-2 space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-slate-600">NF-e Processadas</span>
                    <span className="font-bold text-emerald-600">47</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-slate-600">Em Conferência</span>
                    <span className="font-bold text-blue-600">12</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-slate-600">Despachadas</span>
                    <span className="font-bold text-amber-600">23</span>
                  </div>
                </div>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>

          <SidebarFooter className="border-t border-slate-200 p-4">
            <div className="flex items-center gap-3">
              <div className="w-9 h-9 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold text-sm">
                  {currentUser?.full_name?.charAt(0) || 'U'}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="font-semibold text-slate-900 text-sm truncate">
                  {currentUser?.full_name || 'Usuário'}
                </p>
                <p className="text-xs text-slate-500 truncate capitalize">
                  {currentUser?.role === 'admin' ? 'Administrador' :
                   currentUser?.role === 'manager' ? 'Gestor' :
                   currentUser?.role === 'operator' ? 'Operador' :
                   currentUser?.role === 'verifier' ? 'Conferente' : 'Usuário'}
                </p>
              </div>
              <Button 
                variant="ghost" 
                size="icon"
                onClick={handleLogout}
                className="text-slate-500 hover:text-slate-700 hover:bg-slate-100"
              >
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          </SidebarFooter>
        </Sidebar>

        <main className="flex-1 flex flex-col">
          <header className="bg-white/80 backdrop-blur-sm border-b border-slate-200 px-6 py-4 md:hidden sticky top-0 z-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <SidebarTrigger className="hover:bg-slate-100 p-2 rounded-lg transition-colors duration-200">
                    <Menu className="w-5 h-5 text-slate-500" /> {/* Added Menu icon inside SidebarTrigger */}
                </SidebarTrigger>
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-emerald-600 rounded-lg flex items-center justify-center">
                    <FileText className="w-4 h-4 text-white" />
                  </div>
                  <h1 className="text-lg font-bold text-slate-900">OrderFlow</h1>
                </div>
              </div>
              <Button variant="ghost" size="icon" className="text-slate-500">
                <Bell className="w-5 h-5" />
              </Button>
            </div>
          </header>

          <div className="flex-1 overflow-auto">
            {children}
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
}

