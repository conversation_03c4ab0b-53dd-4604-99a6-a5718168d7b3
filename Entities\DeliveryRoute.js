{"name": "DeliveryRoute", "type": "object", "properties": {"driver_id": {"type": "string", "description": "Driver ID"}, "route_number": {"type": "string", "description": "Route identification number"}, "status": {"type": "string", "enum": ["on_route", "completed"], "default": "on_route", "description": "Route status"}, "departure_date": {"type": "string", "format": "date-time", "description": "Departure date and time"}, "return_date": {"type": "string", "format": "date-time", "description": "Return date and time"}, "total_invoices": {"type": "number", "default": 0, "description": "Total number of invoices in route"}, "delivered_invoices": {"type": "number", "default": 0, "description": "Number of successfully delivered invoices"}, "observations": {"type": "string", "description": "Route observations"}}, "required": ["driver_id", "route_number"]}