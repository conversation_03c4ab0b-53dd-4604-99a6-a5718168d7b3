import React, { useState, useEffect } from "react";
import { Invoice, InvoiceItem, Verification, User } from "@/entities/all";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  CheckCircle, 
  Package, 
  Scan,
  Play,
  Pause,
  RotateCcw,
  AlertTriangle
} from "lucide-react";

import VerificationSelector from "../components/verification/VerificationSelector";
import VerificationInterface from "../components/verification/VerificationInterface";
import VerificationHistory from "../components/verification/VerificationHistory";

export default function Verification() {
  const [availableInvoices, setAvailableInvoices] = useState([]);
  const [activeVerifications, setActiveVerifications] = useState([]);
  const [completedVerifications, setCompletedVerifications] = useState([]);
  const [currentVerification, setCurrentVerification] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("select");

  useEffect(() => {
    loadVerificationData();
  }, []);

  const loadVerificationData = async () => {
    try {
      const [invoices, verifications] = await Promise.all([
        Invoice.filter({ status: "available" }, "-created_date", 50),
        Verification.list("-created_date", 100)
      ]);
      
      setAvailableInvoices(invoices);
      
      // Separate verifications by status
      const active = verifications.filter(v => v.status === 'in_progress' || v.status === 'paused');
      const completed = verifications.filter(v => v.status === 'completed');
      
      setActiveVerifications(active);
      setCompletedVerifications(completed);
    } catch (error) {
      console.error("Error loading verification data:", error);
    }
    setIsLoading(false);
  };

  const handleStartVerification = async (invoice, verificationType) => {
    try {
      const user = await User.me();
      
      // Generate volume code if needed
      let volumeCode = null;
      if (verificationType === 'volume' || verificationType === 'detailed') {
        volumeCode = await generateVolumeCode();
      }
      
      const verification = await Verification.create({
        invoice_id: invoice.id,
        verifier_id: user.id,
        verification_type: verificationType,
        volume_code: volumeCode,
        status: 'in_progress'
      });
      
      // Update invoice status
      await Invoice.update(invoice.id, { 
        status: 'in_verification',
        volume_code: volumeCode,
        verification_type: verificationType
      });
      
      setCurrentVerification(verification);
      setActiveTab("verify");
      loadVerificationData();
    } catch (error) {
      console.error("Error starting verification:", error);
      alert("Erro ao iniciar conferência. Tente novamente.");
    }
  };

  const generateVolumeCode = async () => {
    // Simple volume code generation (A, B, C, ..., AA, AB, etc.)
    const codes = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'];
    // In a real implementation, you'd check which codes are already in use
    return codes[Math.floor(Math.random() * codes.length)];
  };

  const handlePauseVerification = async (verification) => {
    try {
      await Verification.update(verification.id, { status: 'paused' });
      await Invoice.update(verification.invoice_id, { status: 'paused' });
      loadVerificationData();
    } catch (error) {
      console.error("Error pausing verification:", error);
    }
  };

  const handleResumeVerification = async (verification) => {
    try {
      await Verification.update(verification.id, { status: 'in_progress' });
      await Invoice.update(verification.invoice_id, { status: 'in_verification' });
      setCurrentVerification(verification);
      setActiveTab("verify");
      loadVerificationData();
    } catch (error) {
      console.error("Error resuming verification:", error);
    }
  };

  const handleCompleteVerification = async (verification, items) => {
    try {
      await Verification.update(verification.id, { 
        status: 'completed',
        completed_at: new Date().toISOString()
      });
      
      await Invoice.update(verification.invoice_id, { status: 'verified' });
      
      // Save verification items
      for (const item of items) {
        await VerificationItem.create({
          verification_id: verification.id,
          invoice_item_id: item.invoice_item_id,
          quantity_verified: item.quantity_verified,
          ean_used: item.ean_used,
          box_number: item.box_number
        });
      }
      
      setCurrentVerification(null);
      setActiveTab("select");
      loadVerificationData();
      
      alert("Conferência finalizada com sucesso!");
    } catch (error) {
      console.error("Error completing verification:", error);
      alert("Erro ao finalizar conferência. Tente novamente.");
    }
  };

  const getTabCounts = () => {
    return {
      select: availableInvoices.length,
      active: activeVerifications.length,
      history: completedVerifications.length
    };
  };

  const tabCounts = getTabCounts();

  const tabs = [
    { value: "select", label: "Selecionar NF-e", count: tabCounts.select, icon: Package },
    { value: "verify", label: "Conferir", count: 0, icon: Scan },
    { value: "active", label: "Em Andamento", count: tabCounts.active, icon: Play },
    { value: "history", label: "Histórico", count: tabCounts.history, icon: CheckCircle }
  ];

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-slate-200 rounded w-1/3"></div>
          <div className="grid gap-4">
            {Array(3).fill(0).map((_, i) => (
              <div key={i} className="h-32 bg-slate-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="p-6 md:p-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <CheckCircle className="w-8 h-8 text-blue-600" />
              Sistema de Conferência
            </h1>
            <p className="text-slate-600 mt-1">
              Conferência simples, por volume e detalhada com scanner
            </p>
          </div>
          {currentVerification && (
            <Badge className="bg-blue-100 text-blue-700 border-blue-200">
              Conferindo: Volume {currentVerification.volume_code || 'N/A'}
            </Badge>
          )}
        </div>

        {/* Verification Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Disponíveis</p>
                  <p className="text-2xl font-bold text-slate-900">{availableInvoices.length}</p>
                </div>
                <div className="p-3 rounded-xl bg-slate-50 border border-slate-200">
                  <Package className="w-5 h-5 text-slate-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Em Conferência</p>
                  <p className="text-2xl font-bold text-blue-600">{activeVerifications.length}</p>
                </div>
                <div className="p-3 rounded-xl bg-blue-50 border border-blue-200">
                  <Scan className="w-5 h-5 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Finalizadas Hoje</p>
                  <p className="text-2xl font-bold text-emerald-600">
                    {completedVerifications.filter(v => {
                      const today = new Date().toDateString();
                      return new Date(v.completed_at).toDateString() === today;
                    }).length}
                  </p>
                </div>
                <div className="p-3 rounded-xl bg-emerald-50 border border-emerald-200">
                  <CheckCircle className="w-5 h-5 text-emerald-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Pausadas</p>
                  <p className="text-2xl font-bold text-amber-600">
                    {activeVerifications.filter(v => v.status === 'paused').length}
                  </p>
                </div>
                <div className="p-3 rounded-xl bg-amber-50 border border-amber-200">
                  <Pause className="w-5 h-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Verification Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-2">
            <TabsList className="grid w-full grid-cols-4 bg-transparent gap-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger 
                    key={tab.value} 
                    value={tab.value}
                    className="relative data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 border border-transparent rounded-lg px-3 py-2"
                  >
                    <Icon className="w-4 h-4 mr-2" />
                    <span className="text-sm font-medium">{tab.label}</span>
                    {tab.count > 0 && (
                      <Badge 
                        variant="secondary" 
                        className="ml-2 text-xs h-5 w-5 p-0 flex items-center justify-center bg-slate-100 text-slate-700"
                      >
                        {tab.count}
                      </Badge>
                    )}
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </div>

          <TabsContent value="select">
            <VerificationSelector
              invoices={availableInvoices}
              onStartVerification={handleStartVerification}
            />
          </TabsContent>

          <TabsContent value="verify">
            {currentVerification ? (
              <VerificationInterface
                verification={currentVerification}
                onComplete={handleCompleteVerification}
                onPause={() => handlePauseVerification(currentVerification)}
              />
            ) : (
              <Card className="border-none shadow-lg">
                <CardContent className="text-center py-12">
                  <Scan className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-slate-900 mb-2">
                    Nenhuma Conferência Ativa
                  </h3>
                  <p className="text-slate-500 mb-6">
                    Selecione uma NF-e para iniciar a conferência
                  </p>
                  <Button onClick={() => setActiveTab("select")}>
                    Selecionar NF-e
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="active">
            <div className="space-y-4">
              {activeVerifications.length === 0 ? (
                <Card className="border-none shadow-lg">
                  <CardContent className="text-center py-12">
                    <Play className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-slate-900 mb-2">
                      Nenhuma Conferência em Andamento
                    </h3>
                    <p className="text-slate-500">
                      Todas as conferências foram finalizadas
                    </p>
                  </CardContent>
                </Card>
              ) : (
                activeVerifications.map((verification) => (
                  <Card key={verification.id} className="border-none shadow-lg">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-bold text-slate-900">
                            Conferência {verification.volume_code}
                          </h3>
                          <p className="text-slate-600">
                            Tipo: {verification.verification_type === 'simple' ? 'Simples' :
                                   verification.verification_type === 'volume' ? 'Por Volume' : 'Detalhada'}
                          </p>
                          <Badge className={verification.status === 'paused' ? 
                            'bg-amber-100 text-amber-700 border-amber-200' : 
                            'bg-blue-100 text-blue-700 border-blue-200'
                          }>
                            {verification.status === 'paused' ? 'Pausada' : 'Em Progresso'}
                          </Badge>
                        </div>
                        <div className="flex gap-2">
                          {verification.status === 'paused' ? (
                            <Button
                              onClick={() => handleResumeVerification(verification)}
                              className="bg-blue-600 hover:bg-blue-700"
                            >
                              <Play className="w-4 h-4 mr-2" />
                              Retomar
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              onClick={() => handlePauseVerification(verification)}
                            >
                              <Pause className="w-4 h-4 mr-2" />
                              Pausar
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="history">
            <VerificationHistory 
              verifications={completedVerifications}
              onRestart={handleStartVerification}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}