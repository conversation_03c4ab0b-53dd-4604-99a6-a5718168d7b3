{"name": "ProductVariation", "type": "object", "properties": {"product_id": {"type": "string", "description": "Product ID"}, "variation_name": {"type": "string", "description": "Name variation"}, "supplier_cnpj": {"type": "string", "description": "Supplier CNPJ"}, "supplier_name": {"type": "string", "description": "Supplier name"}, "first_occurrence": {"type": "string", "format": "date", "description": "First time this variation appeared"}, "last_occurrence": {"type": "string", "format": "date", "description": "Last time this variation appeared"}, "total_occurrences": {"type": "number", "default": 1, "description": "How many times this variation appeared"}}, "required": ["product_id", "variation_name"]}