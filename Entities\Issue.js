{"name": "Issue", "type": "object", "properties": {"invoice_id": {"type": "string", "description": "Related invoice ID"}, "origin_type": {"type": "string", "enum": ["verification", "dispatch", "manual", "catalog"], "description": "How the issue was created"}, "issue_type": {"type": "string", "enum": ["damaged_product", "missing_product", "wrong_address", "customer_absent", "vehicle_issue", "price_anomaly", "new_product_alert", "other"], "description": "Type of problem"}, "description": {"type": "string", "description": "Issue description"}, "impact_value": {"type": "number", "description": "Financial impact of the issue"}, "responsible_id": {"type": "string", "description": "User who reported the issue"}, "status": {"type": "string", "enum": ["open", "investigating", "resolved", "cancelled"], "default": "open", "description": "Issue status"}, "resolution_date": {"type": "string", "format": "date-time", "description": "When issue was resolved"}, "resolution": {"type": "string", "description": "Issue resolution description"}}, "required": ["origin_type", "issue_type", "description", "responsible_id"]}