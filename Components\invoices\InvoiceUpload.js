import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Upload, 
  FileText, 
  X, 
  Plus, 
  Scan,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { Invoice } from "@/entities/all";
import { UploadFile } from "@/integrations/Core";

export default function InvoiceUpload({ onClose, onSuccess }) {
  const [uploadMethod, setUploadMethod] = useState('file'); // 'file' or 'scanner'
  const [files, setFiles] = useState([]);
  const [accessKeys, setAccess<PERSON><PERSON>s] = useState(['']);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const fileInputRef = useRef(null);

  const handleFileSelect = (e) => {
    const selectedFiles = Array.from(e.target.files).filter(
      file => file.name.toLowerCase().endsWith('.xml')
    );
    
    if (selectedFiles.length === 0) {
      setError("Por favor, selecione apenas arquivos XML");
      return;
    }

    setFiles(prev => [...prev, ...selectedFiles]);
    setError(null);
  };

  const removeFile = (index) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const addAccessKeyField = () => {
    setAccessKeys(prev => [...prev, '']);
  };

  const updateAccessKey = (index, value) => {
    setAccessKeys(prev => {
      const updated = [...prev];
      updated[index] = value;
      return updated;
    });
  };

  const removeAccessKey = (index) => {
    if (accessKeys.length > 1) {
      setAccessKeys(prev => prev.filter((_, i) => i !== index));
    }
  };

  const validateAccessKey = (key) => {
    return key.length === 44 && /^\d+$/.test(key);
  };

  const processFiles = async () => {
    setIsUploading(true);
    setUploadProgress(0);
    setError(null);

    try {
      const totalFiles = files.length;
      const processedFiles = [];

      for (let i = 0; i < totalFiles; i++) {
        const file = files[i];
        
        // Upload file
        const { file_url } = await UploadFile({ file });
        
        // Read file content (simulate XML parsing)
        const reader = new FileReader();
        const xmlContent = await new Promise((resolve, reject) => {
          reader.onload = (e) => resolve(e.target.result);
          reader.onerror = reject;
          reader.readAsText(file);
        });

        // TODO: Parse XML and extract invoice data
        // For now, we'll create a mock invoice
        const mockInvoiceData = {
          access_key: `${Date.now()}${i.toString().padStart(8, '0')}`, // Mock access key
          invoice_number: `${Math.floor(Math.random() * 100000)}`,
          issuer_name: `Fornecedor ${i + 1}`,
          issuer_cnpj: "12.345.678/0001-90",
          recipient_name: "Empresa Destinatária",
          recipient_cnpj: "98.765.432/0001-10",
          total_value: Math.random() * 10000 + 100,
          xml_content: xmlContent,
          status: "available"
        };

        await Invoice.create(mockInvoiceData);
        processedFiles.push(file.name);
        
        setUploadProgress(Math.round(((i + 1) / totalFiles) * 100));
      }

      setSuccess(`${processedFiles.length} NF-e(s) processada(s) com sucesso!`);
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 2000);

    } catch (error) {
      console.error("Error processing files:", error);
      setError("Erro ao processar arquivos. Tente novamente.");
    }
    
    setIsUploading(false);
  };

  const processAccessKeys = async () => {
    setIsUploading(true);
    setUploadProgress(0);
    setError(null);

    try {
      const validKeys = accessKeys.filter(key => key.trim() && validateAccessKey(key.trim()));
      
      if (validKeys.length === 0) {
        setError("Por favor, insira pelo menos uma chave de acesso válida (44 dígitos)");
        setIsUploading(false);
        return;
      }

      const totalKeys = validKeys.length;

      for (let i = 0; i < totalKeys; i++) {
        const accessKey = validKeys[i];
        
        // TODO: Download XML from API using access key
        // For now, we'll create a mock invoice
        const mockInvoiceData = {
          access_key: accessKey,
          invoice_number: `${Math.floor(Math.random() * 100000)}`,
          issuer_name: `Fornecedor da Chave ${accessKey.slice(-8)}`,
          issuer_cnpj: "12.345.678/0001-90",
          recipient_name: "Empresa Destinatária",
          recipient_cnpj: "98.765.432/0001-10",
          total_value: Math.random() * 10000 + 100,
          xml_content: `<xml>Mock XML content for ${accessKey}</xml>`,
          status: "available"
        };

        await Invoice.create(mockInvoiceData);
        setUploadProgress(Math.round(((i + 1) / totalKeys) * 100));
      }

      setSuccess(`${validKeys.length} NF-e(s) baixada(s) e processada(s) com sucesso!`);
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 2000);

    } catch (error) {
      console.error("Error processing access keys:", error);
      setError("Erro ao baixar NF-e(s). Verifique as chaves de acesso.");
    }
    
    setIsUploading(false);
  };

  const handleSubmit = () => {
    if (uploadMethod === 'file') {
      processFiles();
    } else {
      processAccessKeys();
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5 text-blue-600" />
            Adicionar Nova NF-e
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Upload Method Selector */}
          <div className="flex gap-2">
            <Button
              variant={uploadMethod === 'file' ? 'default' : 'outline'}
              onClick={() => setUploadMethod('file')}
              className="flex-1"
            >
              <FileText className="w-4 h-4 mr-2" />
              Upload XML
            </Button>
            <Button
              variant={uploadMethod === 'scanner' ? 'default' : 'outline'}
              onClick={() => setUploadMethod('scanner')}
              className="flex-1"
            >
              <Scan className="w-4 h-4 mr-2" />
              Chave de Acesso
            </Button>
          </div>

          {/* File Upload */}
          {uploadMethod === 'file' && (
            <div className="space-y-4">
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-slate-400 transition-colors">
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept=".xml"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <FileText className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-600 mb-4">
                  Arraste arquivos XML aqui ou clique para selecionar
                </p>
                <Button 
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Selecionar Arquivos
                </Button>
              </div>

              {files.length > 0 && (
                <div className="space-y-2">
                  <Label>Arquivos Selecionados ({files.length})</Label>
                  <div className="max-h-40 overflow-y-auto space-y-2">
                    {files.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-slate-50 rounded-lg">
                        <span className="text-sm text-slate-700">{file.name}</span>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => removeFile(index)}
                          className="h-6 w-6"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Access Key Input */}
          {uploadMethod === 'scanner' && (
            <div className="space-y-4">
              <Label>Chaves de Acesso (44 dígitos cada)</Label>
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {accessKeys.map((key, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      placeholder="Digite ou escaneie a chave de acesso..."
                      value={key}
                      onChange={(e) => updateAccessKey(index, e.target.value)}
                      className={`font-mono ${
                        key && !validateAccessKey(key) ? 'border-red-300 bg-red-50' : ''
                      }`}
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => removeAccessKey(index)}
                      disabled={accessKeys.length === 1}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
              <Button
                variant="outline"
                onClick={addAccessKeyField}
                className="w-full"
              >
                <Plus className="w-4 h-4 mr-2" />
                Adicionar Chave
              </Button>
            </div>
          )}

          {/* Progress */}
          {isUploading && (
            <div className="space-y-2">
              <Label>Processando...</Label>
              <Progress value={uploadProgress} className="h-2" />
              <p className="text-sm text-slate-600">{uploadProgress}% concluído</p>
            </div>
          )}

          {/* Messages */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription className="text-green-700">{success}</AlertDescription>
            </Alert>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose} disabled={isUploading}>
              Cancelar
            </Button>
            <Button 
              onClick={handleSubmit}
              disabled={
                isUploading || 
                (uploadMethod === 'file' && files.length === 0) ||
                (uploadMethod === 'scanner' && !accessKeys.some(key => key.trim()))
              }
            >
              {isUploading ? 'Processando...' : 'Processar NF-e(s)'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}