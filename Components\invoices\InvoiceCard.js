import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Building2, 
  DollarSign, 
  Calendar, 
  MoreVertical,
  FileText,
  CheckCircle,
  Truck,
  AlertTriangle,
  Play,
  Pause,
  Eye
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

export default function InvoiceCard({ invoice, onUpdate }) {
  const getStatusConfig = (status) => {
    const configs = {
      available: {
        label: "Disponível",
        color: "bg-slate-100 text-slate-700 border-slate-200",
        icon: FileText
      },
      in_verification: {
        label: "Em Conferência",
        color: "bg-blue-100 text-blue-700 border-blue-200",
        icon: CheckCircle
      },
      paused: {
        label: "Pausada",
        color: "bg-amber-100 text-amber-700 border-amber-200",
        icon: Pause
      },
      verified: {
        label: "Conferida",
        color: "bg-emerald-100 text-emerald-700 border-emerald-200",
        icon: CheckCircle
      },
      dispatched: {
        label: "Despachada",
        color: "bg-purple-100 text-purple-700 border-purple-200",
        icon: Truck
      },
      completed: {
        label: "Finalizada",
        color: "bg-green-100 text-green-700 border-green-200",
        icon: CheckCircle
      },
      issue: {
        label: "Ocorrência",
        color: "bg-red-100 text-red-700 border-red-200",
        icon: AlertTriangle
      }
    };
    
    return configs[status] || configs.available;
  };

  const getAvailableActions = (status) => {
    switch (status) {
      case 'available':
        return [
          { label: 'Iniciar Conferência', action: 'start_verification', icon: Play }
        ];
      case 'paused':
        return [
          { label: 'Retomar Conferência', action: 'resume_verification', icon: Play }
        ];
      case 'verified':
        return [
          { label: 'Selecionar para Expedição', action: 'select_dispatch', icon: Truck },
          { label: 'Revisar Conferência', action: 'review', icon: Eye }
        ];
      case 'dispatched':
        return [
          { label: 'Finalizar Entrega', action: 'complete', icon: CheckCircle },
          { label: 'Registrar Ocorrência', action: 'create_issue', icon: AlertTriangle }
        ];
      default:
        return [];
    }
  };

  const handleAction = async (action) => {
    // TODO: Implement actions
    console.log(`Action ${action} for invoice ${invoice.id}`);
  };

  const statusConfig = getStatusConfig(invoice.status);
  const StatusIcon = statusConfig.icon;
  const availableActions = getAvailableActions(invoice.status);

  return (
    <Card className="border-none shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4 flex-1">
            {/* Status Icon */}
            <div className={`p-3 rounded-xl ${statusConfig.color} border`}>
              <StatusIcon className="w-5 h-5" />
            </div>
            
            {/* Invoice Info */}
            <div className="flex-1 space-y-3">
              {/* Header */}
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-lg font-bold text-slate-900">
                    NF-e {invoice.invoice_number}
                  </h3>
                  <p className="text-sm text-slate-600 mt-1">
                    Chave: {invoice.access_key?.slice(-8) || 'N/A'}
                  </p>
                </div>
                <Badge className={`${statusConfig.color} border font-medium`}>
                  {statusConfig.label}
                </Badge>
              </div>

              {/* Details Grid */}
              <div className="grid md:grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <Building2 className="w-4 h-4 text-slate-500" />
                  <div>
                    <p className="text-xs text-slate-500">Emitente</p>
                    <p className="font-medium text-sm text-slate-900 truncate">
                      {invoice.issuer_name}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-slate-500" />
                  <div>
                    <p className="text-xs text-slate-500">Valor Total</p>
                    <p className="font-bold text-sm text-slate-900">
                      {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(invoice.total_value)}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-slate-500" />
                  <div>
                    <p className="text-xs text-slate-500">Processada em</p>
                    <p className="font-medium text-sm text-slate-900">
                      {format(new Date(invoice.created_date), "dd/MM/yyyy", { locale: ptBR })}
                    </p>
                  </div>
                </div>
              </div>

              {/* Additional Info */}
              {(invoice.volume_code || invoice.verification_type) && (
                <div className="flex gap-2 pt-2">
                  {invoice.volume_code && (
                    <Badge variant="outline" className="text-xs">
                      Volume: {invoice.volume_code}
                    </Badge>
                  )}
                  {invoice.verification_type && (
                    <Badge variant="outline" className="text-xs capitalize">
                      {invoice.verification_type === 'simple' ? 'Simples' :
                       invoice.verification_type === 'volume' ? 'Por Volume' :
                       invoice.verification_type === 'detailed' ? 'Detalhada' : invoice.verification_type}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          {availableActions.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="text-slate-500 hover:text-slate-700">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {availableActions.map((action, index) => {
                  const ActionIcon = action.icon;
                  return (
                    <DropdownMenuItem 
                      key={index}
                      onClick={() => handleAction(action.action)}
                      className="cursor-pointer"
                    >
                      <ActionIcon className="w-4 h-4 mr-2" />
                      {action.label}
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardContent>
    </Card>
  );
}