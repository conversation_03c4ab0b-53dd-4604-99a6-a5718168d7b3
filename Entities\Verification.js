{"name": "Verification", "type": "object", "properties": {"invoice_id": {"type": "string", "description": "Invoice ID"}, "verifier_id": {"type": "string", "description": "Verifier user ID"}, "verification_type": {"type": "string", "enum": ["simple", "volume", "detailed"], "description": "Type of verification"}, "volume_code": {"type": "string", "description": "Volume code (A, B, AA, etc.)"}, "status": {"type": "string", "enum": ["in_progress", "paused", "completed", "cancelled"], "default": "in_progress", "description": "Verification status"}, "version": {"type": "number", "default": 1, "description": "Version number for restarts"}, "started_at": {"type": "string", "format": "date-time", "description": "When verification started"}, "completed_at": {"type": "string", "format": "date-time", "description": "When verification was completed"}}, "required": ["invoice_id", "verifier_id", "verification_type"]}