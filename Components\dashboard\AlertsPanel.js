import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { 
  AlertTriangle, 
  Clock, 
  Package, 
  TrendingUp,
  ExternalLink
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";

export default function AlertsPanel({ issues }) {
  const getIssueIcon = (type) => {
    const icons = {
      damaged_product: Package,
      missing_product: Package,
      price_anomaly: TrendingUp,
      new_product_alert: Package,
      vehicle_issue: AlertTriangle,
      default: AlertTriangle
    };
    
    const Icon = icons[type] || icons.default;
    return <Icon className="w-4 h-4" />;
  };

  const getIssueColor = (type) => {
    const colors = {
      damaged_product: "bg-red-100 text-red-700 border-red-200",
      missing_product: "bg-orange-100 text-orange-700 border-orange-200",
      price_anomaly: "bg-yellow-100 text-yellow-700 border-yellow-200",
      new_product_alert: "bg-blue-100 text-blue-700 border-blue-200",
      vehicle_issue: "bg-purple-100 text-purple-700 border-purple-200",
      default: "bg-gray-100 text-gray-700 border-gray-200"
    };
    
    return colors[type] || colors.default;
  };

  const getIssueTypeLabel = (type) => {
    const labels = {
      damaged_product: "Produto Danificado",
      missing_product: "Produto Faltando",
      price_anomaly: "Preço Anômalo",
      new_product_alert: "Produto Novo",
      vehicle_issue: "Problema Veículo",
      wrong_address: "Endereço Incorreto",
      customer_absent: "Cliente Ausente",
      other: "Outros"
    };
    
    return labels[type] || "Não Classificado";
  };

  return (
    <Card className="border-none shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-bold text-slate-900 flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-red-500" />
          Alertas e Ocorrências
        </CardTitle>
        {issues.length > 0 && (
          <Badge variant="destructive" className="animate-pulse">
            {issues.length} abertas
          </Badge>
        )}
      </CardHeader>
      <CardContent>
        {issues.length === 0 ? (
          <div className="text-center py-8">
            <AlertTriangle className="w-12 h-12 text-slate-300 mx-auto mb-4" />
            <p className="text-slate-500 mb-4">Nenhuma ocorrência aberta</p>
            <div className="inline-flex items-center gap-2 text-sm text-emerald-600 bg-emerald-50 px-3 py-2 rounded-full">
              <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
              Tudo funcionando bem!
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            {issues.slice(0, 5).map((issue) => (
              <div 
                key={issue.id}
                className="flex items-start gap-3 p-3 rounded-lg bg-slate-50 hover:bg-slate-100 transition-colors border border-slate-200"
              >
                <div className={`p-2 rounded-lg border ${getIssueColor(issue.issue_type)}`}>
                  {getIssueIcon(issue.issue_type)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <p className="font-medium text-slate-900 text-sm">
                      {getIssueTypeLabel(issue.issue_type)}
                    </p>
                    <Badge variant="outline" className="text-xs">
                      {issue.status === 'open' ? 'Aberta' : 
                       issue.status === 'investigating' ? 'Investigando' : 'Resolvida'}
                    </Badge>
                  </div>
                  
                  <p className="text-xs text-slate-600 mb-2 line-clamp-2">
                    {issue.description}
                  </p>
                  
                  <div className="flex items-center gap-2 text-xs text-slate-500">
                    <Clock className="w-3 h-3" />
                    {formatDistanceToNow(new Date(issue.created_date), {
                      addSuffix: true,
                      locale: ptBR
                    })}
                    {issue.impact_value && (
                      <>
                        <span className="text-slate-300">•</span>
                        <span className="font-medium text-red-600">
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(issue.impact_value)}
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {issues.length > 5 && (
              <div className="text-center pt-2">
                <Link to={createPageUrl("Issues")}>
                  <Button variant="ghost" size="sm" className="text-slate-600 hover:text-slate-900">
                    Ver mais {issues.length - 5} ocorrências
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </Button>
                </Link>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}