import React, { useState, useEffect } from "react";
import { Product, ProductVariation } from "@/entities/all";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Package, 
  Search, 
  Filter,
  TrendingUp,
  TrendingDown,
  Minus,
  BarChart3,
  Eye,
  AlertTriangle,
  Star
} from "lucide-react";

import ProductCard from "../components/catalog/ProductCard";
import ProductDetails from "../components/catalog/ProductDetails";
import CatalogFilters from "../components/catalog/CatalogFilters";
import CatalogStats from "../components/catalog/CatalogStats";

export default function Catalog() {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [filters, setFilters] = useState({
    category: "all",
    trend: "all",
    priceRange: "all"
  });

  useEffect(() => {
    loadProducts();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [products, activeTab, searchTerm, filters]);

  const loadProducts = async () => {
    try {
      const data = await Product.list("-last_entry", 100);
      setProducts(data);
    } catch (error) {
      console.error("Error loading products:", error);
    }
    setIsLoading(false);
  };

  const applyFilters = () => {
    let filtered = products;

    // Filter by tab
    if (activeTab === "trending") {
      filtered = filtered.filter(p => p.frequency_trend === "growing");
    } else if (activeTab === "new") {
      filtered = filtered.filter(p => p.total_entries <= 3);
    } else if (activeTab === "alerts") {
      filtered = filtered.filter(p => 
        p.frequency_trend === "declining" || 
        p.total_variations > 5 ||
        (p.max_price - p.min_price) > (p.average_price * 0.3)
      );
    }

    // Apply search
    if (searchTerm) {
      filtered = filtered.filter(product => 
        product.main_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.ean.includes(searchTerm) ||
        product.category?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply filters
    if (filters.category !== "all") {
      filtered = filtered.filter(p => p.category === filters.category);
    }

    if (filters.trend !== "all") {
      filtered = filtered.filter(p => p.frequency_trend === filters.trend);
    }

    setFilteredProducts(filtered);
  };

  const getTabCounts = () => {
    return {
      all: products.length,
      trending: products.filter(p => p.frequency_trend === "growing").length,
      new: products.filter(p => p.total_entries <= 3).length,
      alerts: products.filter(p => 
        p.frequency_trend === "declining" || 
        p.total_variations > 5 ||
        (p.max_price - p.min_price) > (p.average_price * 0.3)
      ).length
    };
  };

  const tabCounts = getTabCounts();

  const tabs = [
    { value: "all", label: "Todos", count: tabCounts.all, icon: Package },
    { value: "trending", label: "Em Alta", count: tabCounts.trending, icon: TrendingUp },
    { value: "new", label: "Novos", count: tabCounts.new, icon: Star },
    { value: "alerts", label: "Alertas", count: tabCounts.alerts, icon: AlertTriangle }
  ];

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-slate-200 rounded w-1/4"></div>
          <div className="grid md:grid-cols-3 lg:grid-cols-4 gap-4">
            {Array(8).fill(0).map((_, i) => (
              <div key={i} className="h-48 bg-slate-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="p-6 md:p-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <Package className="w-8 h-8 text-purple-600" />
              Catálogo Inteligente
            </h1>
            <p className="text-slate-600 mt-1">
              Sistema automático de catalogação e análise de produtos
            </p>
          </div>
          <Button 
            variant="outline"
            onClick={() => setSelectedProduct(null)}
            className="border-purple-200 hover:bg-purple-50"
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            Relatórios
          </Button>
        </div>

        {/* Stats Overview */}
        <CatalogStats products={products} />

        {/* Search and Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <Input
                placeholder="Buscar por nome, EAN ou categoria..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <CatalogFilters 
              filters={filters}
              onFiltersChange={setFilters}
              products={products}
            />
          </div>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Products List */}
          <div className="lg:col-span-3">
            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-2">
                <TabsList className="grid w-full grid-cols-4 bg-transparent gap-1">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <TabsTrigger 
                        key={tab.value} 
                        value={tab.value}
                        className="relative data-[state=active]:bg-purple-50 data-[state=active]:text-purple-700 data-[state=active]:border-purple-200 border border-transparent rounded-lg px-3 py-2"
                      >
                        <Icon className="w-4 h-4 mr-2" />
                        <span className="text-sm font-medium">{tab.label}</span>
                        {tab.count > 0 && (
                          <Badge 
                            variant="secondary" 
                            className="ml-2 text-xs h-5 w-5 p-0 flex items-center justify-center bg-slate-100 text-slate-700"
                          >
                            {tab.count}
                          </Badge>
                        )}
                      </TabsTrigger>
                    );
                  })}
                </TabsList>
              </div>

              <TabsContent value={activeTab}>
                {filteredProducts.length === 0 ? (
                  <Card className="border-none shadow-lg">
                    <CardContent className="text-center py-12">
                      <Package className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-slate-900 mb-2">
                        Nenhum produto encontrado
                      </h3>
                      <p className="text-slate-500">
                        Ajuste os filtros ou aguarde o processamento de novas NF-e
                      </p>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredProducts.map((product) => (
                      <ProductCard 
                        key={product.id} 
                        product={product}
                        onClick={() => setSelectedProduct(product)}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>

          {/* Product Details Sidebar */}
          <div className="lg:col-span-1">
            <ProductDetails 
              product={selectedProduct}
              onClose={() => setSelectedProduct(null)}
            />
          </div>
        </div>
      </div>
    </div>
  );
}