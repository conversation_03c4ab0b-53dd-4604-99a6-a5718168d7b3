import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON>r, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts";

export default function InvoiceStatusChart({ data }) {
  const statusData = [
    { name: 'Disponível', value: data.totalInvoices - data.inVerification - data.dispatched - data.completed, color: '#64748b' },
    { name: 'Conferência', value: data.inVerification, color: '#3b82f6' },
    { name: 'Despachada', value: data.dispatched, color: '#f59e0b' },
    { name: 'Finalizada', value: data.completed, color: '#10b981' },
  ];

  const weeklyData = [
    { name: 'Seg', invoices: 45 },
    { name: 'Ter', invoices: 38 },
    { name: 'Qua', invoices: 52 },
    { name: '<PERSON>ui', invoices: 41 },
    { name: 'Sex', invoices: 67 },
    { name: '<PERSON><PERSON><PERSON>', invoices: 23 },
    { name: '<PERSON>', invoices: 12 },
  ];

  return (
    <div className="grid md:grid-cols-2 gap-6">
      {/* Status Distribution */}
      <Card className="border-none shadow-lg">
        <CardHeader>
          <CardTitle className="text-lg font-bold text-slate-900">
            Distribuição por Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={240}>
            <PieChart>
              <Pie
                data={statusData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={90}
                paddingAngle={2}
                dataKey="value"
              >
                {statusData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value, name) => [value, name]}
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
          <div className="grid grid-cols-2 gap-2 mt-4">
            {statusData.map((item, index) => (
              <div key={index} className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: item.color }}
                />
                <span className="text-xs text-slate-600">{item.name}</span>
                <span className="text-xs font-semibold text-slate-900">{item.value}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Weekly Activity */}
      <Card className="border-none shadow-lg">
        <CardHeader>
          <CardTitle className="text-lg font-bold text-slate-900">
            Atividade da Semana
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={240}>
            <BarChart data={weeklyData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
              <XAxis 
                dataKey="name" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#64748b' }}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#64748b' }}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Bar 
                dataKey="invoices" 
                fill="#3b82f6"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}