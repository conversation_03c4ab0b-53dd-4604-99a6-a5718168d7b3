import React, { useState, useEffect } from "react";
import { Driver, DeliveryRoute, User } from "@/entities/all";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  DollarSign, 
  TrendingUp, 
  Calendar, 
  Users,
  FileText,
  CreditCard,
  BarChart3,
  Receipt
} from "lucide-react";

import PaymentManager from "../components/financial/PaymentManager";
import FinancialReports from "../components/financial/FinancialReports";
import DriverPayments from "../components/financial/DriverPayments";

export default function Financial() {
  const [drivers, setDrivers] = useState([]);
  const [routes, setRoutes] = useState([]);
  const [payments, setPayments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [financialData, setFinancialData] = useState({
    totalPending: 0,
    totalPaid: 0,
    monthlyTotal: 0,
    activeDrivers: 0
  });

  useEffect(() => {
    loadFinancialData();
  }, []);

  const loadFinancialData = async () => {
    try {
      const [driversData, routesData] = await Promise.all([
        Driver.filter({ active: true }),
        DeliveryRoute.list("-created_date", 100)
      ]);
      
      setDrivers(driversData);
      setRoutes(routesData);
      
      // Calculate financial metrics
      calculateFinancialMetrics(driversData, routesData);
    } catch (error) {
      console.error("Error loading financial data:", error);
    }
    setIsLoading(false);
  };

  const calculateFinancialMetrics = (driversData, routesData) => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    let totalPending = 0;
    let totalPaid = 0;
    let monthlyTotal = 0;
    
    // Calculate based on driver payment models and routes
    driversData.forEach(driver => {
      const driverRoutes = routesData.filter(route => route.driver_id === driver.id);
      const monthlyRoutes = driverRoutes.filter(route => {
        const routeDate = new Date(route.departure_date);
        return routeDate.getMonth() === currentMonth && routeDate.getFullYear() === currentYear;
      });
      
      let driverMonthlyTotal = 0;
      
      if (driver.payment_model === 'daily_fixed') {
        // Calculate based on working days
        const workingDays = monthlyRoutes.length > 0 ? new Set(monthlyRoutes.map(r => 
          new Date(r.departure_date).toDateString()
        )).size : 0;
        driverMonthlyTotal = workingDays * (driver.daily_rate || 0);
      } else if (driver.payment_model === 'per_delivery') {
        driverMonthlyTotal = monthlyRoutes.reduce((sum, route) => 
          sum + (route.delivered_invoices * (driver.delivery_rate || 0)), 0);
      } else if (driver.payment_model === 'fixed_plus_route') {
        const workingDays = monthlyRoutes.length > 0 ? new Set(monthlyRoutes.map(r => 
          new Date(r.departure_date).toDateString()
        )).size : 0;
        const routeBonus = monthlyRoutes.length * (driver.route_rate || 0);
        driverMonthlyTotal = (workingDays * (driver.daily_rate || 0)) + routeBonus;
      }
      
      monthlyTotal += driverMonthlyTotal;
      totalPending += driverMonthlyTotal; // Assuming all pending for demo
    });
    
    setFinancialData({
      totalPending,
      totalPaid,
      monthlyTotal,
      activeDrivers: driversData.length
    });
  };

  const tabs = [
    { value: "overview", label: "Visão Geral", icon: BarChart3 },
    { value: "payments", label: "Pagamentos", icon: CreditCard },
    { value: "drivers", label: "Motoristas", icon: Users },
    { value: "reports", label: "Relatórios", icon: FileText }
  ];

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-slate-200 rounded w-1/3"></div>
          <div className="grid md:grid-cols-4 gap-4">
            {Array(4).fill(0).map((_, i) => (
              <div key={i} className="h-32 bg-slate-200 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="p-6 md:p-8 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <DollarSign className="w-8 h-8 text-emerald-600" />
              Gestão Financeira
            </h1>
            <p className="text-slate-600 mt-1">
              Controle de pagamentos, relatórios e análises financeiras
            </p>
          </div>
          <Button className="bg-emerald-600 hover:bg-emerald-700">
            <Receipt className="w-4 h-4 mr-2" />
            Gerar Relatório
          </Button>
        </div>

        {/* Financial Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Pagamentos Pendentes</p>
                  <p className="text-2xl font-bold text-red-600">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(financialData.totalPending)}
                  </p>
                </div>
                <div className="p-3 rounded-xl bg-red-50 border border-red-200">
                  <CreditCard className="w-5 h-5 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Pagamentos Realizados</p>
                  <p className="text-2xl font-bold text-emerald-600">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(financialData.totalPaid)}
                  </p>
                </div>
                <div className="p-3 rounded-xl bg-emerald-50 border border-emerald-200">
                  <TrendingUp className="w-5 h-5 text-emerald-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Total do Mês</p>
                  <p className="text-2xl font-bold text-slate-900">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(financialData.monthlyTotal)}
                  </p>
                </div>
                <div className="p-3 rounded-xl bg-blue-50 border border-blue-200">
                  <Calendar className="w-5 h-5 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Motoristas Ativos</p>
                  <p className="text-2xl font-bold text-slate-900">
                    {financialData.activeDrivers}
                  </p>
                </div>
                <div className="p-3 rounded-xl bg-purple-50 border border-purple-200">
                  <Users className="w-5 h-5 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Financial Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-2">
            <TabsList className="grid w-full grid-cols-4 bg-transparent gap-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger 
                    key={tab.value} 
                    value={tab.value}
                    className="relative data-[state=active]:bg-emerald-50 data-[state=active]:text-emerald-700 data-[state=active]:border-emerald-200 border border-transparent rounded-lg px-3 py-2"
                  >
                    <Icon className="w-4 h-4 mr-2" />
                    <span className="text-sm font-medium">{tab.label}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </div>

          <TabsContent value="overview">
            <div className="grid lg:grid-cols-2 gap-6">
              <Card className="border-none shadow-lg">
                <CardHeader>
                  <CardTitle>Resumo Mensal</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-slate-50 rounded-lg">
                      <span className="text-slate-600">Total de Entregas</span>
                      <span className="font-bold">{routes.length}</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-slate-50 rounded-lg">
                      <span className="text-slate-600">Média por Motorista</span>
                      <span className="font-bold">
                        {financialData.activeDrivers > 0 ? 
                          new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(financialData.monthlyTotal / financialData.activeDrivers) : 'R$ 0,00'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-slate-50 rounded-lg">
                      <span className="text-slate-600">Custo por Entrega</span>
                      <span className="font-bold">
                        {routes.length > 0 ? 
                          new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(financialData.monthlyTotal / routes.length) : 'R$ 0,00'
                        }
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-none shadow-lg">
                <CardHeader>
                  <CardTitle>Próximos Pagamentos</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {drivers.slice(0, 5).map((driver) => (
                      <div key={driver.id} className="flex items-center justify-between p-3 border border-slate-200 rounded-lg">
                        <div>
                          <p className="font-medium text-slate-900">{driver.name}</p>
                          <p className="text-sm text-slate-500 capitalize">
                            {driver.payment_model === 'daily_fixed' ? 'Diária Fixa' :
                             driver.payment_model === 'per_delivery' ? 'Por Entrega' : 'Fixa + Rota'}
                          </p>
                        </div>
                        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                          Pendente
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="payments">
            <PaymentManager 
              drivers={drivers}
              routes={routes}
              onUpdate={loadFinancialData}
            />
          </TabsContent>

          <TabsContent value="drivers">
            <DriverPayments 
              drivers={drivers}
              routes={routes}
              onUpdate={loadFinancialData}
            />
          </TabsContent>

          <TabsContent value="reports">
            <FinancialReports 
              drivers={drivers}
              routes={routes}
              financialData={financialData}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}