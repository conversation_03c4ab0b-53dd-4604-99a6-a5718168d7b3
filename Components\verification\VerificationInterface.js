import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Package, 
  Scan, 
  Plus, 
  Minus,
  Check,
  X,
  AlertTriangle
} from "lucide-react";

export default function VerificationInterface({ invoice, items, onUpdateQuantity, progress }) {
  const [scanMode, setScanMode] = useState(false);
  const [scanInput, setScanInput] = useState('');
  
  const handleQuantityChange = (item, change) => {
    const newQuantity = Math.max(0, (item.quantity_verified || 0) + change);
    onUpdateQuantity(item.id, newQuantity);
  };

  const handleDirectInput = (item, value) => {
    const quantity = parseInt(value) || 0;
    onUpdateQuantity(item.id, quantity);
  };

  const handleScanSubmit = (e) => {
    e.preventDefault();
    if (!scanInput.trim()) return;
    
    // Find item by EAN
    const foundItem = items.find(item => item.ean === scanInput);
    if (foundItem) {
      handleQuantityChange(foundItem, 1);
      setScanInput('');
    } else {
      alert('Produto não encontrado nesta NF-e');
    }
  };

  const getItemStatus = (item) => {
    if (item.quantity_verified === null || item.quantity_verified === undefined) {
      return 'pending';
    }
    if (item.quantity_verified === item.quantity_invoice) {
      return 'correct';
    }
    return 'divergent';
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'correct':
        return <Badge className="bg-emerald-100 text-emerald-700 border-emerald-200">Correto</Badge>;
      case 'divergent':
        return <Badge className="bg-amber-100 text-amber-700 border-amber-200">Divergência</Badge>;
      default:
        return <Badge variant="outline">Pendente</Badge>;
    }
  };

  const pendingItems = items.filter(item => getItemStatus(item) === 'pending');
  const correctItems = items.filter(item => getItemStatus(item) === 'correct');
  const divergentItems = items.filter(item => getItemStatus(item) === 'divergent');

  return (
    <div className="grid lg:grid-cols-4 gap-6">
      {/* Scanner/Input Section */}
      <div className="lg:col-span-1 space-y-6">
        {/* Scanner Toggle */}
        <Card className="border-none shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Scan className="w-5 h-5 text-blue-600" />
              Modo de Entrada
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Button
                variant={!scanMode ? 'default' : 'outline'}
                onClick={() => setScanMode(false)}
                className="flex-1"
              >
                Manual
              </Button>
              <Button
                variant={scanMode ? 'default' : 'outline'}
                onClick={() => setScanMode(true)}
                className="flex-1"
              >
                Scanner
              </Button>
            </div>
            
            {scanMode && (
              <form onSubmit={handleScanSubmit} className="space-y-3">
                <Input
                  placeholder="Escaneie ou digite o EAN..."
                  value={scanInput}
                  onChange={(e) => setScanInput(e.target.value)}
                  className="font-mono"
                  autoFocus
                />
                <Button type="submit" className="w-full">
                  <Scan className="w-4 h-4 mr-2" />
                  Processar
                </Button>
              </form>
            )}
          </CardContent>
        </Card>

        {/* Progress Summary */}
        <Card className="border-none shadow-lg">
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-slate-900">{progress}%</div>
                <p className="text-sm text-slate-600">Concluído</p>
              </div>
              <Progress value={progress} className="h-2" />
              
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center">
                  <div className="font-bold text-emerald-600">{correctItems.length}</div>
                  <div className="text-slate-500">Corretos</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-amber-600">{divergentItems.length}</div>
                  <div className="text-slate-500">Divergentes</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-slate-600">{pendingItems.length}</div>
                  <div className="text-slate-500">Pendentes</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Items List */}
      <div className="lg:col-span-3">
        <Card className="border-none shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="w-5 h-5 text-purple-600" />
              Itens para Conferência ({items.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {items.map((item) => {
                const status = getItemStatus(item);
                return (
                  <div
                    key={item.id}
                    className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                      status === 'correct' ? 'border-emerald-200 bg-emerald-50' :
                      status === 'divergent' ? 'border-amber-200 bg-amber-50' :
                      'border-slate-200 bg-white hover:border-slate-300'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-slate-900 leading-tight">
                              {item.description}
                            </h4>
                            {item.ean && (
                              <p className="text-xs text-slate-500 font-mono mt-1">
                                EAN: {item.ean}
                              </p>
                            )}
                          </div>
                          {getStatusBadge(status)}
                        </div>

                        <div className="grid md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-slate-500">NF-e:</span>
                            <span className="ml-2 font-medium">{item.quantity_invoice} {item.unit}</span>
                          </div>
                          <div>
                            <span className="text-slate-500">Conferido:</span>
                            <span className="ml-2 font-medium">
                              {item.quantity_verified ?? 0} {item.unit}
                            </span>
                          </div>
                          <div>
                            <span className="text-slate-500">Unitário:</span>
                            <span className="ml-2 font-medium">
                              {new Intl.NumberFormat('pt-BR', {
                                style: 'currency',
                                currency: 'BRL'
                              }).format(item.unit_price)}
                            </span>
                          </div>
                        </div>

                        {status === 'divergent' && (
                          <div className="flex items-center gap-2 p-2 bg-amber-100 rounded text-xs text-amber-700">
                            <AlertTriangle className="w-3 h-3" />
                            <span>
                              Diferença: {(item.quantity_verified || 0) - item.quantity_invoice} {item.unit}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleQuantityChange(item, -1)}
                          disabled={scanMode}
                        >
                          <Minus className="w-4 h-4" />
                        </Button>
                        
                        <Input
                          type="number"
                          value={item.quantity_verified ?? 0}
                          onChange={(e) => handleDirectInput(item, e.target.value)}
                          className="w-20 text-center font-medium"
                          min="0"
                          disabled={scanMode}
                        />
                        
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleQuantityChange(item, 1)}
                          disabled={scanMode}
                        >
                          <Plus className="w-4 h-4" />
                        </Button>

                        {status === 'correct' && (
                          <div className="ml-2 p-1 bg-emerald-100 rounded-full">
                            <Check className="w-4 h-4 text-emerald-600" />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}