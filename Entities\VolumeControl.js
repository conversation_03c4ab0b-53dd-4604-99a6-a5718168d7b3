{"name": "VolumeControl", "type": "object", "properties": {"volume_code": {"type": "string", "description": "Volume code (A, B, AA, etc.)"}, "invoice_id": {"type": "string", "description": "Invoice ID using this code"}, "assigned_at": {"type": "string", "format": "date-time", "description": "When code was assigned"}, "released_at": {"type": "string", "format": "date-time", "description": "When code was released"}, "status": {"type": "string", "enum": ["in_use", "available"], "default": "in_use", "description": "Code status"}}, "required": ["volume_code"]}