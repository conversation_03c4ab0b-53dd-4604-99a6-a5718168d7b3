{"name": "InvoiceItem", "type": "object", "properties": {"invoice_id": {"type": "string", "description": "Invoice ID"}, "product_code": {"type": "string", "description": "Product internal code"}, "description": {"type": "string", "description": "Product description"}, "unit": {"type": "string", "description": "Unit of measure"}, "quantity_invoice": {"type": "number", "description": "Quantity declared in invoice"}, "quantity_verified": {"type": "number", "default": 0, "description": "Quantity verified during check"}, "unit_price": {"type": "number", "description": "Unit price"}, "total_price": {"type": "number", "description": "Total line value"}, "ean": {"type": "string", "description": "EAN barcode"}, "box_number": {"type": "number", "description": "Box number for detailed verification"}}, "required": ["invoice_id", "description", "quantity_invoice", "unit_price"]}