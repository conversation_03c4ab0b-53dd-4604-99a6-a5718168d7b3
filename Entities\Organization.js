{"name": "Organization", "type": "object", "properties": {"name": {"type": "string", "description": "Organization name"}, "cnpj": {"type": "string", "description": "Brazilian tax ID"}, "plan": {"type": "string", "enum": ["basic", "premium", "enterprise"], "default": "basic", "description": "Subscription plan"}, "status": {"type": "string", "enum": ["active", "suspended", "cancelled"], "default": "active", "description": "Organization status"}, "settings": {"type": "object", "description": "Organization-specific settings"}}, "required": ["name"]}