{"name": "Payment", "type": "object", "properties": {"driver_id": {"type": "string", "description": "Driver ID"}, "payment_type": {"type": "string", "enum": ["daily", "weekly", "monthly", "per_delivery"], "description": "Payment type"}, "reference_period": {"type": "string", "description": "Reference period (YYYY-MM or YYYY-MM-DD)"}, "base_amount": {"type": "number", "description": "Base payment amount"}, "bonus_amount": {"type": "number", "default": 0, "description": "Additional bonus amount"}, "total_amount": {"type": "number", "description": "Total payment amount"}, "deliveries_count": {"type": "number", "default": 0, "description": "Number of deliveries in period"}, "routes_count": {"type": "number", "default": 0, "description": "Number of routes in period"}, "working_days": {"type": "number", "default": 0, "description": "Number of working days"}, "status": {"type": "string", "enum": ["pending", "paid", "cancelled"], "default": "pending", "description": "Payment status"}, "paid_at": {"type": "string", "format": "date-time", "description": "When payment was made"}, "notes": {"type": "string", "description": "Payment notes"}}, "required": ["driver_id", "payment_type", "reference_period", "total_amount"]}